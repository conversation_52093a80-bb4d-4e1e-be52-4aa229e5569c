import MarkdownParser from '@/components/business/markdown-parser'
import ReferenceList from '../../../../basic-search/chat/ReferenceList'
import { Copy } from '@/components/business/copy'
import Image from 'next/image'
import { SourceTypeEnum } from '@/api/report/saveSource'
import { MessageRoleEnum, ReferenceItem } from '@/api/getSession'
import { cn } from '@/lib/utils'

export const ChatPreview = ({ chat, className = 'w-[760px]' }) => {
  const joinReference = (list?: ReferenceItem[]): string => {
    if ((list && list.length === 0) || !list) return ''
    const text = list
      .map((item, idx) => {
        return ` ${idx + 1} ${item.title} ${item.url}`
      })
      .join('\n')

    return `\n\n${t(`chat.refer`)}\n\n${text}`
  }

  return (
    <div className='hide-scrollbar m-auto h-screen-minus-header overflow-scroll py-9'>
      <div className={cn('m-auto', className)}>
        {chat.messages.map((item, index) => {
          return (
            <div key={index} className='flex p-3'>
              <div className='shrink-0'>
                <Image
                  src={
                    item.role === MessageRoleEnum.USER
                      ? '/images/default_avatar.svg'
                      : '/images/system_avatar.svg'
                  }
                  width={32}
                  height={32}
                  className='mr-3 rounded-lg'
                  alt={'avatar'}
                />
              </div>

              <div className='w-[calc(100%-32px)] flex-1'>
                {item.role === MessageRoleEnum.USER ? (
                  index === 0 && <MarkdownParser content={item.content[0]?.value || item.content} />
                ) : (
                  <div>
                    <MarkdownParser content={item.content} list={chat.referenceList} />
                    <ReferenceList referenceList={item.referenceList} />
                    <Copy
                      question={
                        index === 1
                          ? chat.messages[0]?.content
                          : chat.messages[index - 1]?.content[0]?.value
                      }
                      sourceType={SourceTypeEnum.CHAT}
                      sourceFromId={chat.sessionId as string}
                      content={item.content + joinReference(item.referenceList)}
                      sourceId={chat.id}
                      procId={item.processId}
                    />
                  </div>
                )}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
