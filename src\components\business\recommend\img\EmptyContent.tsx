import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import Image from 'next/image'
import { TextEnum, Text } from '../../text'

const EmptyConent = () => {
  const { t } = useTranslation(Namespace.BASIC_SEARCH)

  return (
    <div className='flex-center w-full flex-grow flex-col'>
      <Image src='/images/no_data.svg' alt='no data' width={48} height={48} />
      <Text type={TextEnum.Body_medium} className='mt-1 text-gray'>
        {t('chat.rightSide.noData')}
      </Text>
    </div>
  )
}

export default EmptyConent
