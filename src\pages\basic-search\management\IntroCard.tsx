import * as React from 'react'
import { Card } from '@/components/ui/card'
import { Text, TextEnum } from '@/components/business/text'
import { SparklesIcon } from '@heroicons/react/24/outline'
import { PoTypeEnum } from '@/types'
interface IntroCardProps {
  title: string
  content: React.ReactNode
  onClick: (index: PoTypeEnum) => void
  type: PoTypeEnum
}
const IntroCard: React.FC<IntroCardProps> = ({ title, content, onClick, type }) => {
  return (
    <Card
      className='w-max-[350px] cursor-pointer p-4 text-left'
      onClick={() => {
        onClick(type)
      }}>
      <SparklesIcon width={20} height={20} />
      <Text type={TextEnum.H4} className='mt-6'>
        {title}
      </Text>
      <Text type={TextEnum.Body_medium}>{content}</Text>
    </Card>
  )
}

export default IntroCard
