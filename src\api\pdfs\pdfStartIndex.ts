import { fetchWithAuth } from '@/lib/fetch'
import { PDFItemType } from './getPdf'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface PdfStartIndexRequest {
  pdfId: string
}

export type PdfStartIndexResponse = {
  pdf: PDFItemType
}

export const pdfStartIndex = async (data: PdfStartIndexRequest): Promise<PdfStartIndexResponse> => {
  const response = await fetchWithAuth<PdfStartIndexResponse>(`${apiUrl}/pdf/startIndex`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
