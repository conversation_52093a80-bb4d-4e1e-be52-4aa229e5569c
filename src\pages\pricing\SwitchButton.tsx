import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import clsx from 'clsx'
import Image from 'next/image'

interface SwitchButtonProps {
  type: 'month' | 'year'
  onClick: (newType: 'month' | 'year') => void
}
const SwitchButton: React.FC<SwitchButtonProps> = ({ type, onClick }: SwitchButtonProps) => {
  const { t } = useTranslation(Namespace.PRICING)

  const handleClick = () => {
    if (onClick) onClick(type === 'month' ? 'year' : 'month')
  }

  return (
    <div
      className='flex-center relative mb-12 mt-11 h-8 cursor-pointer rounded-[56px] bg-secondary px-1 py-[3px]'
      onClick={handleClick}>
      <div
        className={clsx(
          'flex-center mr-1 h-[26px] w-[82px] rounded-[34px] text-[14px] leading-[16px]',
          type === 'month'
            ? 'ai-smarties-color-bg font-medium text-white'
            : 'font-normal text-secondary-black-3',
        )}>
        {t('switchBtnText.monthly')}
      </div>
      <div
        className={clsx(
          'flex-center h-[26px] w-[82px] rounded-[34px] text-[14px] leading-[16px]',
          type === 'year'
            ? 'ai-smarties-color-bg font-medium text-white'
            : 'font-normal text-secondary-black-3',
        )}>
        {t('switchBtnText.year')}
      </div>
      <Image
        src='/images/save.svg'
        alt='no data'
        width={134}
        height={65}
        className={clsx(
          'absolute bottom-[-8px] left-[156px]',
          type === 'month' ? 'visible' : 'invisible',
        )}
      />
    </div>
  )
}

export default SwitchButton
