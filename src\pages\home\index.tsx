import { PageHeader } from '@/components/business/page-header'
import { Text, TextEnum } from '@/components/business/text'
import { Namespace } from '@/i18n'
import { smartiesRoutes } from '@/routers'
import {
  BookOpenIcon,
  ChevronRightIcon,
  CubeIcon,
  MagnifyingGlassIcon,
} from '@heroicons/react/24/outline'
import clsx from 'clsx'
import { useRouter } from 'next/router'
import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import styleNames from './index.module.scss'
interface Sub {
  title: string
  desc: string
}

const Home = () => {
  const { t } = useTranslation([Namespace.GLOBAL, Namespace.HOME])
  const router = useRouter()
  const tabs = useMemo(() => {
    const subs1: Sub[] = t('home:subs1', { returnObjects: true })
    const subs2: Sub[] = t('home:subs2', { returnObjects: true })
    const subs3: Sub[] = t('home:subs3', { returnObjects: true })

    const classes = clsx('mr-2 h-6 w-6 text-primary')

    return [
      {
        title: t('home:resource1.title'),
        icon: <MagnifyingGlassIcon className={classes} />,
        path: smartiesRoutes.basicSearch.home,
        subs: subs1,
      },
      {
        title: t('home:resource2.title'),
        path: smartiesRoutes.deepExplore.fileDisk,
        icon: <CubeIcon className={classes} />,
        subs: subs2,
      },
      {
        title: t('home:resource3.title'),
        path: smartiesRoutes.document,
        icon: <BookOpenIcon className={classes} />,
        subs: subs3,
      },
    ]
  }, [t])
  // useEffect(() => {
  //   Tracking.trackPageView({ eventName: 'VISIT_HOME_PAGE' })
  // }, [])
  return (
    <div
      className='flex h-screen w-screen flex-col bg-background'
      asm-tracking='TEST_VISIT_HOME_PAGE:VIEW'>
      <div className='float-none h-16 w-full'>
        <PageHeader tabName='home' />
      </div>
      <div className='flex-center h-screen-minus-header'>
        <div className='flex flex-col'>
          <div className='flex-center flex-1 text-center'>
            <Text type={TextEnum.H1} className='max-w-[900px] text-foreground'>
              {t('home:title')}
            </Text>
          </div>

          <div className='flex-h-center mt-[110px] w-full flex-row gap-4'>
            {tabs.map((item) => {
              return (
                <div
                  key={item.title}
                  className={`w-[345px] min-w-[300px] rounded-lg ${styleNames.item}`}
                  onClick={() => item.path && router.push(item.path)}>
                  <div className='cursor-pointer rounded-lg bg-primary-bg p-3 pt-0'>
                    <div className='flex-v-center p-4 pl-0'>
                      {item.icon}
                      <Text type={TextEnum.H4} className='leading-7 text-primary'>
                        {item.title}
                      </Text>
                      <div className='flex flex-1 justify-end'>
                        <ChevronRightIcon className='h-6 w-6 text-primary' />
                      </div>
                    </div>
                    <div className='flex flex-col gap-2'>
                      {item.subs.map((sub) => {
                        return (
                          <div
                            key={sub.title}
                            className={`flex-v-center cursor-pointer justify-between rounded-sm bg-card p-4`}>
                            <div>
                              <Text type={TextEnum.H4} className='text-foreground'>
                                {sub.title}
                              </Text>
                              <Text type={TextEnum.Body_medium} className='mt-2 text-foreground'>
                                {sub.desc}
                              </Text>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </div>
  )
}

export default Home
