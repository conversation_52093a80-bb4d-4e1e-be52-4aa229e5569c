import { Text, TextEnum } from '@/components/business/text'
import { Input } from '@/components/ui/input'
import React, { useState } from 'react'
import { PaperAirplaneIcon, StopCircleIcon } from '@heroicons/react/24/outline'
import { Namespace } from '@/i18n'
import clsx from 'clsx'
import { useTranslation } from 'react-i18next'
export type InputSize = 'large' | 'medium' | 'small'

export interface AskInputProps {
  disabled?: boolean
  size?: InputSize
  showStop?: boolean
  placeholder?: string
  className?: string
  withIcon?: boolean
  onSearch?: (key: string) => void
  onStop?: () => void
  onChange?: (key: string) => void
  maxLength?: number
  type?: string
}

export const AskInput: React.FC<AskInputProps> = ({
  onSearch,
  onStop,
  size = 'medium',
  disabled = false,
  showStop,
  placeholder,
  className,
  withIcon = true,
  onChange,
  ...props
}) => {
  const { t } = useTranslation(Namespace.BASIC_SEARCH)
  const [searchString, setSearchString] = useState('')
  const [isComposing, setIsComposing] = useState(false)

  const classes: Record<InputSize, string> = {
    large: 'h-14 text-base',
    medium: 'h-12 ',
    small: 'h-10 text-sm',
  }

  const handleIconClick = () => {
    if (showStop && onStop) {
      onStop()
    }
    if (!showStop && !disabled && onSearch) {
      setSearchString('')
      onSearch(searchString)
    }
  }

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchString(event.target.value)
    if (onChange) onChange(event.target.value)
  }

  const handleCompositionStart = () => {
    setIsComposing(true) // 开始使用输入法
  }

  const handleCompositionEnd = () => {
    setIsComposing(false) // 输入法输入完成
  }

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter' && !isComposing) {
      handleIconClick()
    }
  }

  return (
    <>
      <div className={clsx('relative flex items-center', className)}>
        <Input
          {...props}
          value={searchString}
          className={clsx('bg-card', { 'pr-14': withIcon }, classes[size])}
          placeholder={placeholder}
          disabled={disabled}
          onChange={handleInputChange}
          onCompositionStart={handleCompositionStart}
          onCompositionEnd={handleCompositionEnd}
          onKeyDown={handleKeyDown}
        />
        {withIcon && (
          <div
            className={clsx(
              'absolute right-2 top-2 flex h-[30px] transform items-center justify-center rounded-sm bg-primary px-[7px] text-white',
              disabled ? 'bg-primary-disabled' : 'cursor-pointer',
              showStop ? '' : '',
            )}
            onClick={handleIconClick}>
            {showStop ? (
              <>
                <StopCircleIcon className='mr-1 h-4 w-4' />
                <Text type={TextEnum.Body_medium}> {t('common.stop')}</Text>
              </>
            ) : (
              <PaperAirplaneIcon className='h-4 w-4' />
            )}
          </div>
        )}
      </div>
    </>
  )
}
