import { Modal } from '@/components/business/modal'
import { TextEnum, Text } from '@/components/business/text'
import { Button } from '@/components/ui/button'
import { Namespace } from '@/i18n'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

export interface DeleteMenuModalProps {
  open: boolean
  onClose: () => void
  onConfirm: () => void
}
const ModalDelete: React.FC<DeleteMenuModalProps> = ({
  open,
  onClose,
  onConfirm,
}: DeleteMenuModalProps) => {
  const { t } = useTranslation(Namespace.GLOBAL)

  const [disabled, setDisabled] = useState(false)

  const handleCloseModal = () => {
    if (onClose) onClose()
  }

  const handleClickConfirm = () => {
    setDisabled(true)
    if (onConfirm) {
      onConfirm()
      setDisabled(false)
    }
  }

  return (
    <>
      <Modal open={open} className='relative min-w-[420px] p-6'>
        <Text type={TextEnum.H4} className='flex items-center justify-between'>
          {t('global:tip')}
          <XMarkIcon onClick={handleCloseModal} className='h-5 w-5 cursor-pointer' />
        </Text>
        <div>
          <Text type={TextEnum.Body_big} className='mt-4'>
            {t('global:modal.deleteContentDocument')}
          </Text>
        </div>
        <div className='mt-5 flex w-full justify-end gap-2'>
          <Button variant='secondary' onClick={handleCloseModal}>
            {t('global:button.cancel')}
          </Button>
          <Button onClick={handleClickConfirm} disabled={disabled}>
            {t('global:button.confirm')}
          </Button>
        </div>
      </Modal>
    </>
  )
}

export default ModalDelete
