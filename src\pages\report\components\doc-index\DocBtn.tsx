import { Text, TextEnum } from '@/components/business/text'
import { But<PERSON> } from '@/components/ui/button'
import CreditsModal from '@/components/business/credits-modal/CreditsModal'
import { checkCredits } from '@/pages/basic-search/management/index'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import { getUser } from '@/api/getUser'
import { ModalDeduct } from '../modal/ModalDeduct'
import { GeneratorStatus } from '@/api/document/index'
import { useReport } from '../../store/useReport'
import { useGenerate } from '../../store/useGenerate'
import { useState } from 'react'
import { useUserStore } from '@/store/userStore'
import { Tracking } from '@/lib/tracking'
import { TrackingEventType } from '@/lib/tracking/types'
import { useDownloadMarkdown } from '@/lib/hooks/useDownloadMarkdown'
import ShareLink from '@/components/business/share-link'
import { ShareTypeEnum } from '@/types'

export const DocBtn = () => {
  const { t } = useTranslation(Namespace.GENERATOR)
  const [visibleDeduct, setVisibleDeduct] = useState(false)
  const [openCreditsModal, setOpenCreditsModal] = useState(false)
  const generateDocument = useGenerate((s) => s.generateDocument)
  const cancelGenerateDocument = useGenerate((s) => s.cancelGenerateDocument)
  const currentReport = useReport((s) => s.currentReport)
  const updateCurrentReport = useReport((s) => s.updateCurrentReport)
  const [disable, setDisabled] = useState(false)
  const updateUser = useUserStore((state) => state.updateUser)
  const user = useUserStore((state) => state.user)
  const { exportToWord } = useDownloadMarkdown(currentReport)

  // 生成文档
  const handleClick = async () => {
    setDisabled(true)
    if (currentReport.documentGeneratorStatus === GeneratorStatus.FINISH) {
      exportToWord(`${currentReport.title}.docx`)
      setDisabled(false)
      return
    }
    if (
      currentReport.documentGeneratorStatus === GeneratorStatus.WAITING ||
      currentReport.documentGeneratorStatus === GeneratorStatus.CREATING
    ) {
      // 取消生成
      await cancelGenerateDocument(currentReport.reportId)
      updateCurrentReport({ documentGeneratorStatus: GeneratorStatus.INIT })
      await updateCredits()
      setDisabled(false)
      return
    }
    // 首先检查��余积分是否足够
    const checkRes = checkCredits(user, 3)
    if (!checkRes) return setOpenCreditsModal(true)
    // 积分消费提示4积分，是否不再显示
    const modalDeduct = localStorage.getItem('ModalDeduct')
    if (!JSON.parse(modalDeduct)) return setVisibleDeduct(true)
    Tracking.trackEvent('REPORT_GENERATE', TrackingEventType.CLICK, {
      value: currentReport.title,
    })
    await generateDocument(currentReport.reportId)
    await updateCredits()
    updateCurrentReport({
      documentGeneratorStatus: GeneratorStatus.WAITING,
    })
    setDisabled(false)
  }
  const onConfirmDeduct = async (key) => {
    setVisibleDeduct(false)
    localStorage.setItem('ModalDeduct', JSON.stringify(key))
    Tracking.trackEvent('REPORT_GENERATE', TrackingEventType.CLICK, {
      value: currentReport.title,
    })
    await generateDocument(currentReport.reportId)
    await updateCredits()
    updateCurrentReport({
      documentGeneratorStatus: GeneratorStatus.WAITING,
    })
    setDisabled(false)
  }
  const updateCredits = async () => {
    const user = await getUser()
    if (user) updateUser(user)
    return user
  }

  const buttonText = () => {
    const map = {
      [GeneratorStatus.INIT]: t('autoGenerator'),
      [GeneratorStatus.WAITING]: t('cancelGenerating'),
      [GeneratorStatus.CREATING]: t('cancelGenerating'),
      [GeneratorStatus.FINISH]: t('download'),
      [GeneratorStatus.FAILED]: t('autoGenerator'),
    }
    return map[currentReport.documentGeneratorStatus]
  }
  return (
    <>
      <div className='mr-2 mt-[10px]'>
        <ShareLink shareType={ShareTypeEnum.REPORT} shareData={{ title: currentReport.title }} />
      </div>
      <Button className='mt-[10px] h-8 px-3 py-0' onClick={handleClick} disabled={disable}>
        <Text type={TextEnum.Body_medium} className='flex-v-center'>
          {buttonText()}
          {disable && <img src='/images/loading.svg' className='mr-1 h-4 w-4 animate-spin-slow' />}
        </Text>
      </Button>
      <CreditsModal
        open={openCreditsModal}
        onClose={() => {
          setDisabled(false)
          setOpenCreditsModal(false)
        }}
      />
      <ModalDeduct
        open={visibleDeduct}
        onConfirm={onConfirmDeduct}
        handleCancel={() => {
          setDisabled(false)
          setVisibleDeduct(false)
        }}
      />
    </>
  )
}
