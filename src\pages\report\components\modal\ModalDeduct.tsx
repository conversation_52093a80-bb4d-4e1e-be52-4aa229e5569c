import { Modal } from '@/components/business/modal'
import { Button } from '@/components/ui/button'
import { Text, TextEnum } from '@/components/business/text'
import { XMarkIcon } from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import { Checkbox } from '@/components/ui/checkbox'
import { useState } from 'react'
export const ModalDeduct = ({ open, handleCancel, onConfirm }) => {
  const { t } = useTranslation(Namespace.GENERATOR)
  const [check, setCheck] = useState(false)
  const onCheckedChange = (checked) => {
    setCheck(checked)
  }
  return (
    <Modal open={open} className='w-[434px] break-all p-6'>
      <div>
        <Text type={TextEnum.H4} className='flex items-center justify-between'>
          {t('deductTitle')}
          <XMarkIcon onClick={handleCancel} className='h-5 w-5 cursor-pointer' />
        </Text>
        <div className='mt-4'>
          <Text type={TextEnum.Body_medium}>{t('promptContent')}</Text>
          <div className='mt-3 flex items-center'>
            <div className='flex-v-center'>
              <Checkbox checked={check} onCheckedChange={onCheckedChange} />
              <Text type={TextEnum.Body_medium} className='ml-2'>
                {t('showAgain')}
              </Text>
            </div>
          </div>
          <div className='mt-2 text-right'>
            <Button variant='secondary' onClick={handleCancel}>
              {t('cancel')}
            </Button>
            <Button
              className='ml-2'
              onClick={() => {
                onConfirm(check)
              }}>
              {t('confirm')}
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  )
}
