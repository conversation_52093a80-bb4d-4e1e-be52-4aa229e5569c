import { Skeleton } from '@/components/ui/skeleton'
import clsx from 'clsx'
export const DocSkeleton = ({ className, rows = 1 }) => {
  const list = new Array(rows).fill('')
  return (
    <>
      {list.map((_, idx) => (
        <div className='flex gap-2 px-3 py-2' key={idx}>
          <Skeleton className={clsx(className)} />
        </div>
      ))}
    </>
  )
}

export const SkeletonCard = () => {
  return (
    <div className='hide-scrollbar flex h-screen-minus-header w-full overflow-auto bg-card'>
      <div className='flex w-64 shrink-0 flex-col border-r-[1px] border-border px-3 pt-5'>
        <div>
          <Skeleton className='rounded-x h-7 w-full' />
        </div>
        <div className='mt-5 space-y-6'>
          {new Array(8).fill('').map((_, idx) => (
            <div className='flex w-full' key={idx}>
              <Skeleton className='mr-2 h-3 w-3' />
              <Skeleton className='h-3 w-full' />
            </div>
          ))}
          {new Array(2).fill('').map((_, idx) => (
            <div className='flex w-[70%]' key={idx}>
              <Skeleton className='mr-2 h-3 w-3' />
              <Skeleton className='h-3 w-full' />
            </div>
          ))}
          {new Array(2).fill('').map((_, idx) => (
            <div className='flex w-[40%]' key={idx}>
              <Skeleton className='mr-2 h-3 w-3' />
              <Skeleton className='h-3 w-full' />
            </div>
          ))}
        </div>
      </div>
      <div className='w-[calc(100vw-256px-370px)] shrink-0 px-[74px] pt-[46px]'>
        <DocumentLoading></DocumentLoading>
      </div>
      <div className='hide-scrollbar w-[370px] shrink-0 overflow-auto border-l-[1px] border-border px-4 pt-[37px]'>
        <div className='space-y-3'>
          <Skeleton className='h-8 w-[60%]' />
          <Skeleton className='h-[77px] w-full' />
          <Skeleton className='h-32 w-full' />
          <Skeleton className='h-[77px] w-full' />
          <Skeleton className='h-[220px] w-full' />
        </div>
      </div>
    </div>
  )
}

export const DocumentLoading = () => {
  return (
    <div className='flex flex-col'>
      <div className='space-y-3'>
        <Skeleton className='h-7 w-full' />
        <Skeleton className='h-7 w-[calc(100%/3)]' />
      </div>
      <div className='mt-11 space-y-3'>
        <Skeleton className='h-5 w-full' />
        <Skeleton className='h-5 w-full' />
        <Skeleton className='h-5 w-[calc(40%)]' />
      </div>
      <div className='mt-11 space-y-3'>
        <Skeleton className='h-5 w-full' />
        <Skeleton className='h-5 w-full' />
        <Skeleton className='h-5 w-full' />
        <Skeleton className='h-5 w-[calc(20%)]' />
      </div>
      <div className='mt-11 space-y-3'>
        <Skeleton className='h-5 w-full' />
        <Skeleton className='h-5 w-full' />
        <Skeleton className='h-5 w-full' />
        <Skeleton className='h-5 w-full' />
        <Skeleton className='h-5 w-[calc(10%)]' />
      </div>
    </div>
  )
}
