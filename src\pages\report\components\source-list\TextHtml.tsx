import { Text, TextEnum } from '@/components/business/text'
import MarkdownParser from '@/components/business/markdown-parser'

import { cn } from '@/lib/utils'
export const TextHtml = ({ className, text }) => {
  return (
    <Text type={TextEnum.Body_small}>
      <div className={cn('max-h-[224px] overflow-hidden text-ellipsis break-words', className)}>
        <MarkdownParser content={text}></MarkdownParser>
      </div>
    </Text>
  )
}
