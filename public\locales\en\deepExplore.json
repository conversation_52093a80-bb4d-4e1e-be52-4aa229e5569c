{"importFile": {"button": "Import Files", "title": "Import Files", "upload": {"upload": "Click to upload", "paste": "or <1>Paste a URL</1>", "limit": "Supports PDF format, up to {{count}} files，max {{size}} size each.", "limitOrImg": "Supports PDF and Image format, up to {{count}} files，max {{size}} size each.", "fileNumError": "You can only upload up to {{count}} files in total.", "fileTypeError": "{{name}} is not a valid PDF file.", "fileSizeError": "{{name}} exceeds the maximum file size.", "fileTypeErrorOrImg": "{{name}} is not a valid PDF or Image file.", "uploadFailed": "Upload Failed", "followingUploadFailed": "The following file failed to upload:", "uploadLimitError": "The space capacity has been exceeded, cannot upload again."}, "paste": {"title": "Paste a PDF Link here"}}, "auto": "Auto", "pdfCard": {"failedTitle": "File Download Failed", "uploadingTitle": "File Uploading...", "tags": {"downloading": "Downloading", "downloadFailure": "Download Failed", "uploading": "Uploading", "processing": "Processing", "success": "Success", "failure": "Fail"}}, "loading": "Loading...", "downloadFailed": "Cannot access this file, download Failed.", "removePdf": "Remove this record", "retry": "Retry", "download": "Download", "status": {"processing": "Processing", "failed": "Failed", "previewFailed": "Preview failed", "loading": "Loading...", "fileTooLarge": "The PDF file is too large, please upgrade to Pro first.", "overMaxLimits": "The PDF is too large, cannot process it.", "exceedLimit": "Sorry the file is too large to parse.", "upgradeBtn": "Upgrade"}, "chat": {"summarizePdf": "Summarize", "summarizeMessage": "Summarize this file", "inputPlacehoder": "Ask anything..."}, "statustext": {"summarize": "summarizing"}, "fileCheck": {"failed": "The link is not a valid FPD file"}, "deleteModal": {"title": "Tips", "content": "Are you sure to delete this file?"}, "search": {"PDF": "PDFs", "IMG": "Images", "ALL": "ALL", "sequence": "Newest uploads first", "reverse": "Oldest uploads first"}, "selectTip": "{{num}} item has been selected", "totalFile": "Total {{count}} items"}