import { loginEmail, LoginEmailResponse } from '@/api/loginEmail'
import { sendVerifyEmail } from '@/api/sendVerifyEmail'
import { AuthError } from '@/api/types'
import { updateSetting } from '@/api/updateSetting'
import { AuthLogin } from '@/components/business/accounts/auth'
import { EmailLogin } from '@/components/business/accounts/email'
import { AccountLayout } from '@/components/business/accounts/layout'
import { ResendEmail } from '@/components/business/accounts/resend'
import { AccountTitle } from '@/components/business/accounts/title'
import { Text, TextEnum } from '@/components/business/text'
import useIsMobileScreen from '@/components/ui/useIsMobile'
import { Namespace, SupportedLangs } from '@/i18n'
import useLanguage from '@/i18n/useLanguage'
import { clearCookies } from '@/lib/auth'
import { getCookie, setCookie, smartiesDomain } from '@/lib/cookie'
import { smartiesRoutes } from '@/routers'
import { SubscriptionEnum } from '@/types'
import clsx from 'clsx'
import { Session } from 'next-auth'
import { getSession, signIn } from 'next-auth/react'
import { useRouter } from 'next/router'
import { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'sonner'

type PageStatus = 'init' | 'active'

const Login = () => {
  const { t } = useTranslation([Namespace.GLOBAL, Namespace.LOGIN])
  const router = useRouter()
  const { query } = router
  const referralCode = query.referralCode as string | undefined
  const [emailLoginDisabled, setEmailLoginDisabled] = useState(false)
  const [loading, setLoading] = useState(true)

  const { language, changeLanguage } = useLanguage({ loggedIn: false })

  const texts = {
    title: t('login:login.title'),
    desc: t('login:login.desc'),
    email: t('login:input.email'),
    emailPlaceholder: t('login:input.emailPlaceholder'),
    password: t('login:input.password'),
    passwordPlaceholder: t('login:input.passwordPlaceholder'),
    passwordTip: t('login:input.passwordTip'),
    passwordForgot: t('login:input.passwordForgot'),
    button: t('login:login.button'),
  }

  const isFetching = useRef(false)
  const redirectUrlRef = useRef('')
  const handleLogin = async ({ type }: { type?: 'google' | 'linkedin' }) => {
    clearCookies()
    // 提供给iframe使用
    const baseUrl = redirectUrlRef.current
      ? redirectUrlRef.current
      : `${window.location.origin}/${smartiesRoutes.login}`
    const callbackUrl = `${baseUrl}?auth=${type}${referralCode ? `&referralCode=${referralCode}` : ''}`
    await signIn(type, { callbackUrl, referralCode })
  }

  const [pageStatus, setPageStatus] = useState<PageStatus>('init')
  const [email, setEmail] = useState('')
  const userIdRef = useRef<string>()
  const [isIframe, setIsIframe] = useState(false)

  useEffect(() => {
    // Tracking.trackPageView({ eventName: 'VISIT_LOGIN_PAGE' })
    if (query?.lang && query?.redirectUrl) {
      setIsIframe(true)
      changeLanguage(query.lang as string)
      redirectUrlRef.current = query.redirectUrl as string
    }
    console.log('0', router.pathname, router.query?.auth)
    if (!query.auth) {
      setLoading(false)
      console.log('1')
      return
    }
    console.log('2')
    if (!isFetching.current) {
      isFetching.current = true
      console.log('3')
      getSession()
        .then((data) => {
          console.log('4')
          const sessionData = data as Session & {
            smartiesToken?: string
            userId: string
            quota: number
            usage: number
            currentPlan: SubscriptionEnum
          }
          if (sessionData?.smartiesToken && sessionData.user) {
            console.log('5')
            setCookie('smartToken', sessionData.smartiesToken, {
              maxAge: 7 * 24 * 60 * 60 * 1000,
              httpOnly: false,
              domain: smartiesDomain,
            })
            updateSetting({
              language: (language ?? getCookie('lang')) as SupportedLangs,
            }).then(() => {
              console.log('6')
              isFetching.current = false
              router.push(smartiesRoutes.basicSearch.home)
            })
          } else {
            console.log('7')
            isFetching.current = false
            setLoading(false)
          }
        })
        .catch(() => {
          console.log('8')
          isFetching.current = false
          setLoading(false)
        })
    }
    // 重定向 redirect
  }, [])

  const [isMobile] = useIsMobileScreen()

  // 语言不走json包，这个是固定的
  // 语言选择框，不跟 mode走，因为首页是固定深色的
  return loading ? null : (
    <AccountLayout isIframe={isIframe}>
      {pageStatus === 'init' && (
        <>
          <div
            className={clsx('flex-center w-full flex-col')}
            asm-tracking={'TEST_VISIT_LOGIN_PAGE:VIEW'}>
            <AccountTitle title={texts.title} desc={texts.desc} />
            <EmailLogin
              texts={texts}
              buttonDisabled={emailLoginDisabled}
              suffix={
                <div
                  onClick={() => router.push({ pathname: smartiesRoutes.resetPassword, query })}
                  className='w-full cursor-pointer text-left text-sm font-medium text-white'>
                  {texts.passwordForgot}
                </div>
              }
              onClick={(email, password) => {
                ;(async () => {
                  setEmailLoginDisabled(true)
                  const res = await loginEmail({ email: email.trim(), password: password.trim() })
                  if ((res as AuthError).errCode) {
                    const response = res as AuthError
                    if (response.errCode === 'notVerified') {
                      setPageStatus('active')
                      setEmail(email)
                      userIdRef.current = (response as unknown as LoginEmailResponse).userId
                    } else if (response.errCode === 'invalidCredentials') {
                      toast(t('login:errorMessage.loginError'), {
                        duration: 3000,
                      })
                    } else if (
                      response.errCode === 'parameterError' &&
                      response.errMsg === 'email'
                    ) {
                      toast(t('login:errorMessage.accountError'), {
                        duration: 3000,
                      })
                    } else {
                      toast(t('login:errorMessage.common'), {
                        duration: 3000,
                      })
                    }
                  } else {
                    const response = res as LoginEmailResponse
                    if (response.userId) {
                      await sendVerifyEmail({
                        userId: response.userId!,
                        language: language as SupportedLangs,
                      })
                    }
                    if (response.accessToken) {
                      setCookie('smartToken', response.accessToken, {
                        maxAge: 7 * 24 * 60 * 60 * 1000,
                        httpOnly: false,
                        domain: smartiesDomain,
                      })
                      if (isIframe) {
                        window.parent.postMessage({ type: 'loginSuccess' }, '*')
                      } else {
                        router.push(smartiesRoutes.basicSearch.home)
                      }
                    }
                  }
                })().finally(() => setEmailLoginDisabled(false))
              }}
            />
            <div className={clsx('flex-center', isMobile ? 'w-11/12' : 'w-[420px]')}>
              <AuthLogin onAuthLogin={handleLogin} />
            </div>
            <Text
              type={TextEnum.Body_medium}
              className={'mt-10 w-[420px] text-center !leading-6 text-white'}>
              {t('login:account.withoutAccountStatus')}
              <span
                className='cursor-pointer text-primary'
                onClick={() => router.push({ pathname: smartiesRoutes.signup, query })}>
                &nbsp; {t('login:account.withoutAccountAction')} &nbsp;
              </span>
            </Text>
          </div>
        </>
      )}

      {pageStatus === 'active' ? (
        <ResendEmail
          email={email}
          disabled={emailLoginDisabled}
          desc={t('login:reactive', {
            email: email,
          })}
          onResend={() => {
            ;(async () => {
              setEmailLoginDisabled(true)
              const res = await sendVerifyEmail({
                userId: userIdRef.current!,
                language: language as SupportedLangs,
              })
              if ((res as AuthError).errCode) {
                toast((res as AuthError).errMsg, {
                  duration: 3000,
                })
              } else {
                toast(t('login:toast.resend'), {
                  duration: 3000,
                })
              }
            })().finally(() => setEmailLoginDisabled(false))
          }}
        />
      ) : null}
    </AccountLayout>
  )
}

export default Login
