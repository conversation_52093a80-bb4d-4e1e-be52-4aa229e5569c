import { StarIcon } from '@heroicons/react/24/solid'
import { ArrowTopRightOnSquareIcon } from '@heroicons/react/24/outline'
import { Text, TextEnum } from '@/components/business/text'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'

export const Overly = ({ setOverly, onCancelSave, onOpenDialog }) => {
  const { t } = useTranslation([Namespace.BASIC_SEARCH])
  return (
    <div
      className='flex-center absolute left-0 top-0 h-full w-full cursor-pointer rounded-sm bg-card-95'
      onMouseLeave={setOverly}>
      <div className='flex-center h-full w-full px-5'>
        <div onClick={onOpenDialog} className='flex-center p-1 hover:bg-primary-bg'>
          <ArrowTopRightOnSquareIcon className='mb-1 h-4 w-4' />
          <Text type={TextEnum.Body_medium} className='ml-1 mr-1'>
            {t('chat.rightSide.open')}
          </Text>
        </div>
        <div onClick={onCancelSave} className='flex-center p-1 hover:bg-primary-bg'>
          <StarIcon className='mb-1 ml-1 h-4 w-4 text-[#FABC44]' />
          <Text type={TextEnum.Body_medium} className='ml-1 mr-1'>
            {t('chat.rightSide.saveToDoc')}
          </Text>
        </div>
      </div>
    </div>
  )
}
