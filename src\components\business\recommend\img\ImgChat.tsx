import { ChangeEvent, useEffect, useRef, useState } from 'react'
import { TextEnum, Text } from '../../text'
import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import StopModal from '@/pages/basic-search/chat/StopModal'
import { SparklesIcon } from '@heroicons/react/24/outline'
import { MessageRoleEnum } from '@/api/getSession'
import MarkdownParser from '../../markdown-parser'
import { Copy } from '../../copy'
import LoadingDots from '../../loading-dots'
import ChatLoading from './ChatLoading'
import EmptyConent from './EmptyContent'
import SmartiesAvatar from './SmartiesAvatar'
import { MediaItemType } from '@/api/getMedia'
import { imageStart, QuickImageInquiryEnum } from '@/api/imgs/imageStart'
import { imageSession, MediaMessageType } from '@/api/imgs/imageSession'
import { imageRunning, ImageStatusEnum, MediaProcessType } from '@/api/imgs/imageRunning'
import { usePollingTask as createPollingTask } from '@/lib/hooks/usePollingTask'
import { imageProcess } from '@/api/imgs/imageProcess'
import CreditsModal from '../../credits-modal/CreditsModal'
import { imageCancel } from '@/api/imgs/imageCancel'
import { Textinput } from '../../text-input'
import { useUserStore } from '@/store/userStore'
import { SubscriptionEnum } from '@/types'
import { getUser } from '@/api/getUser'
import { Tracking } from '@/lib/tracking'
import { TrackingEventType } from '@/lib/tracking/types'
import { SourceTypeEnum } from '@/api/report/saveSource'
import { SelectDropdown } from '@/components/business/select-dropdown'
import { useRouter } from 'next/router'
import { smartiesRoutes } from '@/routers'

const Tag = ({ text, onClick }: { text: string; onClick: () => void }) => {
  return (
    <div
      className='flex-v-center cursor-pointer rounded-md border border-border bg-card px-3 py-2 hover:bg-secondary-hover'
      onClick={onClick}>
      <SparklesIcon className='mr-1 h-4 w-4' />
      <Text type={TextEnum.Body_medium}>{text}</Text>
    </div>
  )
}

const isProcessFinished = (status: ImageStatusEnum) => {
  return (
    status === ImageStatusEnum.FINISH ||
    status === ImageStatusEnum.FAILED ||
    status === ImageStatusEnum.CANCELED
  )
}

export interface ImgChatProps {
  img: MediaItemType
  source: string
  saveToReport?: boolean
}

interface OptType {
  mediaId: string
  mediaSessionId: string | undefined
  quickInquiry?: QuickImageInquiryEnum
  message?: string
  source?: string
}

const ImgChat: React.FC<ImgChatProps> = ({ img, source, saveToReport = true }: ImgChatProps) => {
  const { t } = useTranslation([Namespace.BASIC_SEARCH, Namespace.GLOBAL])

  const router = useRouter()

  const user = useUserStore((state) => state.user)
  const updateUser = useUserStore((state) => state.updateUser)

  const [messages, setMessages] = useState<MediaMessageType[]>([])
  const [loading, setLoading] = useState(true)

  const [showStop, setShowStop] = useState(false) // bottom input status
  const [openStopModal, setOpenStopModal] = useState(false)

  const lastUserQuestionRef = useRef<HTMLDivElement>(null)
  const [pollingProcess, setPollingProcess] = useState<MediaProcessType | null>(null)

  const timerRef = useRef<ReturnType<typeof createPollingTask> | null>(null)

  const [openCreditsModal, setOpenCreditsModal] = useState(false)

  const mediaSessionIdRef = useRef<string | undefined>(undefined)

  const [inputValue, setInputValue] = useState('')
  const [querying, setQuerying] = useState(false)

  // 当前是否有 failed
  const [queryFailed, setQueryFailed] = useState(false)

  // 创建轮询函数
  useEffect(() => {
    timerRef.current = createPollingTask()
  }, [])

  useEffect(() => {
    // reset
    setMessages([])
    setPollingProcess(null)
    setShowStop(false)
    setQuerying(false)
    timerRef.current?.stop()
    mediaSessionIdRef.current = undefined
    setQueryFailed(false)

    if (img.mediaId) {
      ;(async () => {
        const messages = await queryChatList(img.mediaId)
        setLoading(false)
        setMessages(messages ?? [])
        scrollChatList()
        if (messages && messages?.length > 0) {
          await queryRunningTask({
            mediaId: img.mediaId,
          })
        }
      })()
    } else {
      setLoading(false)
    }
  }, [img.mediaId])

  // get 聊天记录
  const queryChatList = async (mediaId: string): Promise<MediaMessageType[]> => {
    try {
      const chatList = await imageSession({ mediaId })
      if (chatList === null) {
        mediaSessionIdRef.current = undefined
      } else if (chatList.mediaSession.messages) {
        mediaSessionIdRef.current = chatList.mediaSession.mediaSessionId
        return chatList.mediaSession.messages
      }
    } catch (error) {
      return []
    }
    return []
  }

  // 查询未完成的 task & 轮询
  const queryRunningTask = async ({ mediaId }: { mediaId: string }) => {
    try {
      const runningTask = await imageRunning({ mediaId })
      if (runningTask && runningTask.mediaProcess.mediaProcessId) {
        setShowStop(true)
        setQuerying(true)
        setPollingProcess(runningTask.mediaProcess)

        await timerRef.current?.start({
          task: async () => {
            const processDetail = await imageProcess({
              mediaProcessId: runningTask.mediaProcess?.mediaProcessId,
            })
            setPollingProcess(processDetail.mediaProcess ?? null)
            scrollChatList()
            // 追问： ProcessStatusEnum 停止就结束
            if (
              processDetail.mediaProcess?.status &&
              isProcessFinished(processDetail.mediaProcess?.status)
            ) {
              await timerRef.current?.stop()
              setPollingProcess(null)
              const messages = await queryChatList(processDetail.mediaProcess.mediaId)
              setMessages(messages ?? [])
              setShowStop(false)
              setQuerying(false)
              scrollChatList()
              if (processDetail.mediaProcess?.status === ImageStatusEnum.FAILED) {
                setQueryFailed(true)
              }
            }
          },
          interval: 5 * 1000,
          timeout: 10 * 60 * 1000,
          immediate: true,
        })
      }
    } catch (error) {
      setPollingProcess(null)
      setShowStop(false)
      setQuerying(false)
    }
  }

  const updateCredits = async () => {
    const user = await getUser()
    if (user) updateUser(user)
    return user
  }

  // 查询余额
  const checkCredits = async () => {
    // 校验次数
    if (
      user?.currentPlan === SubscriptionEnum.FREE ||
      user?.currentPlan === SubscriptionEnum.BASIC_MONTH ||
      user?.currentPlan === SubscriptionEnum.BASIC_YEAR
    ) {
      if (user.usage.totalQuota - user.usage.totalUsage <= 0) {
        setOpenCreditsModal(true)
        return false
      }
      return true
    } else if (
      // 不校验次数
      user?.currentPlan === SubscriptionEnum.PRO_MONTH ||
      user?.currentPlan === SubscriptionEnum.PRO_YEAR
    ) {
      return true
    } else {
      setOpenCreditsModal(true)
      return false
    }
  }

  const handleSendQuery = async ({
    query,
    quickInquiry,
  }: {
    query?: string
    quickInquiry?: QuickImageInquiryEnum
  }) => {
    const res = await checkCredits()
    if (!res) return

    Tracking.trackEvent('CLICK_START_CHAT_BUTTON', TrackingEventType.CLICK, {
      value: query,
      meta: router.pathname === smartiesRoutes.deepExplore.preview ? 'UPLOAD' : 'SEARCH',
    })

    if ((query && query.trim().length > 0) || quickInquiry) {
      setQueryFailed(false)
      setInputValue('')
      setQuerying(true)
      try {
        const opt: OptType = {
          mediaId: img.mediaId,
          mediaSessionId: mediaSessionIdRef.current,
        }
        if (quickInquiry) {
          opt.quickInquiry = quickInquiry
        } else {
          opt.message = query
        }
        if (source) {
          opt.source = source
        }
        const resp = await imageStart(opt)
        await updateCredits()
        setQuerying(false)
        setShowStop(true)
        if (resp.mediaProcessId) {
          const messages = await queryChatList(img.mediaId)
          setMessages(messages ?? [])
          scrollChatList()
          await queryRunningTask({
            mediaId: img.mediaId,
          })
        }
      } catch (error) {
        setShowStop(false)
        setQuerying(false)
        setQueryFailed(true)
      }
    }
  }

  // 取消
  const handleCancelQuery = async () => {
    if (pollingProcess && pollingProcess.mediaProcessId) {
      timerRef.current?.stop()
      setPollingProcess(null)
      const res = await imageCancel({
        mediaProcessId: pollingProcess.mediaProcessId,
      })
      if (res.mediaProcess.status) {
        setQuerying(false)
        setShowStop(false)
        await updateCredits()
      }
    }
    setOpenStopModal(false)
  }

  // click describle btn
  const handleClickDecribeTag = async () => {
    handleSendQuery({
      quickInquiry: QuickImageInquiryEnum.DESCRIBE,
    })
  }

  const handleChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value)
  }

  // 滚动屏幕
  const scrollChatList = () => {
    setTimeout(() => {
      if (lastUserQuestionRef.current) {
        lastUserQuestionRef.current.scrollIntoView({ behavior: 'smooth' })
      }
    }, 100)
  }
  return (
    <>
      <div className='flex h-[100%] flex-col justify-between p-1'>
        {loading ? (
          <ChatLoading />
        ) : messages.length === 0 ? (
          <EmptyConent />
        ) : (
          <div className='hide-scrollbar flex-1 overflow-scroll' id='ai-smarties-img-chat-content'>
            {messages.map((item, index) => {
              const ref =
                item.role === MessageRoleEnum.USER &&
                (index === messages.length - 1 || index === messages.length - 2)
                  ? lastUserQuestionRef
                  : null

              return (
                <div className='flex p-3' key={index}>
                  <div className='shrink-0'>
                    <SmartiesAvatar type={item.role} className='mr-3' />
                  </div>
                  {item.role === MessageRoleEnum.USER ? (
                    <div ref={ref}>
                      <MarkdownParser content={item.content} />
                    </div>
                  ) : (
                    <div className='w-[calc(100%-32px)] flex-1'>
                      <MarkdownParser content={item.content} />
                      <Copy
                        saveToReport={saveToReport}
                        question={messages[index - 1].content}
                        sourceType={SourceTypeEnum.MEDIA}
                        sourceFromId={img.mediaId}
                        content={item.content}
                        source={source}
                        sourceId={item.reportSourceId}
                        procId={item.mediaProcessId}
                      />
                    </div>
                  )}
                </div>
              )
            })}

            {pollingProcess && !isProcessFinished(pollingProcess.status) && (
              <div className='flex p-3'>
                <div className='shrink-0'>
                  <SmartiesAvatar type={MessageRoleEnum.ASSISTANT} className='mr-3' />
                </div>
                <div className='mt-1 flex items-center gap-2'>
                  <LoadingDots size={4} />
                  <Text type={TextEnum.Body_medium}> {t('chat.statustext.summarize')} </Text>
                </div>
              </div>
            )}

            {queryFailed && (
              <div className='flex p-3'>
                <div className='shrink-0'>
                  <SmartiesAvatar type={MessageRoleEnum.ASSISTANT} className='mr-3' />
                </div>
                <Text type={TextEnum.Body_medium} className='text-dangerous-main mt-2'>
                  {t('global:error.systemError')}
                </Text>
              </div>
            )}
            <SelectDropdown
              dropdownWidth={110}
              type='media'
              sourceId={img.mediaId}
              showListKey={['saveToDoc', 'close']}></SelectDropdown>
          </div>
        )}
        <div className='shrink-0 p-3'>
          <div className='mb-2 flex flex-row gap-1'>
            <Tag text={t('imgChat.describeImg')} onClick={handleClickDecribeTag} />
            {/* <Tag text='Extract all content' /> */}
          </div>
          <Textinput
            withIcon={true}
            placeholder={t('chat.input.placehoder')}
            maxLength={300}
            className='flex-1 shrink-0'
            onSearch={(value) => {
              handleSendQuery({
                query: value,
              })
            }}
            onEnter={(value) => {
              handleSendQuery({
                query: value,
              })
            }}
            showStop={showStop}
            disabled={!querying && inputValue.trim() === ''}
            onStop={() => {
              setOpenStopModal(true)
            }}
            value={inputValue}
            onChange={handleChange}
          />
        </div>
      </div>
      <StopModal
        open={openStopModal}
        onClose={() => {
          setOpenStopModal(false)
        }}
        onConfirm={handleCancelQuery}
      />
      <CreditsModal
        open={openCreditsModal}
        onClose={() => {
          setOpenCreditsModal(false)
        }}
      />
    </>
  )
}

export default ImgChat
