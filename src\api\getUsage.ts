import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL
export interface GetUsageResponse {
  quota: number
  usage: number
}

export const getUsage = async (): Promise<GetUsageResponse> => {
  const response = await fetchWithAuth<GetUsageResponse>(`${apiUrl}/user/getUsage`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  })

  return response
}
