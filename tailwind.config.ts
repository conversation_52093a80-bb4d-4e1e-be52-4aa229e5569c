import type { Config } from 'tailwindcss'
import postcssPxToRem from 'postcss-pxtorem'

const config = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{js,ts,jsx,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: '',
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      fontFamily: {
        roboto: ['var(--font-roboto)'],
      },
      height: {
        'screen-minus-header': 'calc(100vh - 64px - 1px)',
        'screen-minus-2-header': 'calc(100vh - 64px - 44px)',
        'screen-minus-3-header': 'calc(100vh - 64px - 44px - 32px)',
        'screen-minus-pdf-content': 'calc(100vh - 64px - 32px)',
      },
      colors: {
        // Light mode colors
        border: 'var(--border)', // 边框的颜色
        input: 'var(--input)', // 输入框的背景颜色
        ring: 'var(--ring)', // 元素被聚焦时的环形边框颜色
        background: 'var(--background)', // 页面或容器的背景颜色 - 主背景
        foreground: 'var(--foreground)', // 文本和内容的前景颜色 - 主字体色号
        primary: {
          // 主要元素的背景颜色，默认颜色及其前景颜色
          DEFAULT: 'var(--primary)',
          foreground: 'var(--primary-foreground)',
          //
          hover: 'var(--primary-hover)',
          light: 'var(--primary-lighter)',
          dark: 'var(--primary-darker)',
          disabled: 'var(--primary-disabled)',
          bg: 'var(--primary-bg)',
          border: 'var(--primary-300)',
        },
        secondary: {
          // 次要元素的背景颜色，默认颜色及其前景颜色
          DEFAULT: 'var(--secondary)',
          foreground: 'var(--secondary-foreground)',
          //
          'black-1': 'var(--secondary-text-1)',
          'black-2': 'var(--secondary-text-2)',
          'black-3': 'var(--secondary-text-3)',
          acitve: 'var(--secondary-active)',
          hover: 'var(--secondary-hover)',
          disabled: 'var(--secondary-disabled)',
        },
        destructive: {
          // 表示破坏性操作的背景颜色，默认颜色及其前景颜色
          DEFAULT: 'var(--destructive)',
          foreground: 'var(--destructive-foreground)',
        },
        muted: {
          // 禁用或次要的元素的背景颜色，默认颜色及其前景颜色
          DEFAULT: 'var(--muted)',
          foreground: 'var(--muted-foreground)',
        },
        accent: {
          // 强调或特殊元素的背景颜色，默认颜色及其前景颜色
          DEFAULT: 'var(--accent)',
          foreground: 'var(--accent-foreground)',
        },
        'modal-in-card': 'var(--modal-in-card)',
        popover: {
          // 弹出框或浮动元素的背景颜色，默认颜色及其前景颜色
          DEFAULT: 'var(--popover)',
          foreground: 'var(--popover-foreground)',
        },
        card: {
          // 卡片或面板的背景颜色，默认颜色及其前景颜色
          DEFAULT: 'var(--card)',
          foreground: 'var(--card-foreground)',
          '95': 'rgba(var(--card-rgb), 0.95)',
          '15': 'rgba(var(--card-rgb), 0.15)',
        },
        tooltip: {
          DEFAULT: 'var(--tooltip)',
          foreground: 'var(--tooltip-foreground)',
        },
        success: {
          DEFAULT: 'var(--success)',
          '10': 'rgba(var(--success-rgb), 0.1)',
          '50': 'rgba(var(--success-rgb), 0.5)',
        },
        info: {
          DEFAULT: 'var(--info)',
          '10': 'rgba(var(--info-rgb), 0.1)',
        },
        dangerous: {
          DEFAULT: 'var(--dangerous)',
          '10': 'rgba(var(--dangerous-rgb), 0.1)',
          active: 'var(--dangerous-darker)',
          hover: 'var(--dangerous-lighter)',
          disabled: 'var(--dangerous-disabled)',
        },
        warning: {
          DEFAULT: 'var(--warning)',
        },
        gray: {
          DEFAULT: 'var(--gray)',
          imgBg: 'var(--gray-img-bg)',
          tabHeader: 'var(--gray-tab-header)',
        },
        black: {
          1: 'var(--black-1)',
          '1-50': 'rgba(var(--black-1-rgb), 0.5)',
          '1-70': 'rgba(var(--black-1-rgb), 0.7)',
          2: 'var(--black-2)',
        },
        skeleton: 'var(--gray-skeleton)',

        // Dark mode colors
        'dark-border': 'var(--dark-border)',
        'dark-input': 'var(--dark-input)',
        'dark-ring': 'var(--dark-ring)',
        'dark-background': 'var(--dark-background)',
        'dark-foreground': 'var(--dark-foreground-rgb)',
        'dark-primary': {
          DEFAULT: 'var(--dark-primary)',
          foreground: 'var(--dark-primary-foreground)',
          //
          hover: 'var(--dark--primary-hover)',
          light: 'var(--dark--primary-lighter)',
          dark: 'var(--dark--primary-darker)',
          disabled: 'var(--dark--primary-disabled)',
          bg: 'var(--dark--primary-bg)',
          border: 'var(--dark--primary-300)',
        },
        'dark-secondary': {
          DEFAULT: 'var(--dark-secondary))',
          foreground: 'var(--dark-secondary-foreground))',
          //
          'black-1': 'var(--dark--secondary-text-1)',
          'black-2': 'var(--dark--secondary-text-2)',
          'black-3': 'var(--dark--secondary-text-3)',
          acitve: 'var(--dark--secondary-active)',
          hover: 'var(--dark--secondary-hover)',
          disabled: 'var(--dark--secondary-disabled)',
        },
        'dark-destructive': {
          DEFAULT: 'var(--dark-destructive)',
          foreground: 'var(--dark-destructive-foreground)',
        },
        'dark-muted': {
          DEFAULT: 'var(--dark-muted)',
          foreground: 'var(--dark-muted-foreground)',
        },
        'dark-accent': {
          DEFAULT: 'var(--dark-accent)',
          foreground: 'var(--dark-accent-foreground)',
        },
        'dark-modal-in-card': 'var(--dark--modal-in-card)',
        'dark-popover': {
          DEFAULT: 'var(--dark-popover)',
          foreground: 'var(--dark-popover-foreground)',
        },
        'dark-card': {
          DEFAULT: 'var(--dark-card)',
          foreground: 'var(--dark-card-foreground)',
          '95': 'rgba(var(--dark--card-rgb), 0.95)',
          '15': 'rgba(var(--dark--card-rgb), 0.15)',
        },
        'dark-tooltip': {
          DEFAULT: 'var(--dark--tooltip)',
          foreground: 'var(--dark--tooltip-foreground)',
        },
        'dark-success': {
          DEFAULT: 'var(--dark--success)',
          '10': 'rgba(var(--dark--success-rgb), 0.1)',
          '50': 'rgba(var(--dark--success-rgb), 0.5)',
        },
        'dark-info': {
          DEFAULT: 'var(--dark--info)',
          '10': 'rgba(var(--dark--info-rgb), 0.1)',
        },
        'dark-dangerous': {
          DEFAULT: 'var(--dark--dangerous)',
          '10': 'rgba(var(--dark--dangerous-rgb), 0.1)',
          active: 'var(--dark--dangerous-darker)',
          hover: 'var(--dark--dangerous-lighter)',
          disabled: 'var(--dark--dangerous-disabled)',
        },
        'dark-gray': {
          DEFAULT: 'var(--dark--gray)',
          imgBg: 'var(--dark--gray-img-bg)',
          tabHeader: 'var(--dark--gray-tab-header)',
        },
        'dark-black': {
          1: 'var(--dark--black-1)',
          '1-50': 'rgba(var(--dark--black-1-rgb), 0.5)',
          2: 'var(--dark--black-2)',
        },
        'dark-skeleton': 'var(--dark--gray-skeleton)',
      },
      borderRadius: {
        lg: '16px',
        md: '8px',
        sm: '6px',
        xs: '4px',
      },
      fontSize: {
        // 这都是默认值，因为UI组件库里也用了这个，不要随便修改这个值，但可以扩展，这里也基本对应了我们的主题里的字号
        xs: ['0.75rem', { lineHeight: '1rem' }], // 12px, 16px
        sm: ['0.875rem', { lineHeight: '1.25rem' }], // 14px, 20px
        base: ['1rem', { lineHeight: '1.5rem' }], // 16px, 24px
        lg: ['1.125rem', { lineHeight: '1.75rem' }], // 18px, 28px
        xl: ['1.25rem', { lineHeight: '1.75rem' }], // 20px, 28px
        '2xl': ['1.5rem', { lineHeight: '2rem' }], // 24px, 32px
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }], // 30px, 36px
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }], // 36px, 40px
        '5xl': ['3rem', { lineHeight: '1' }], // 48px
        '6xl': ['3.75rem', { lineHeight: '1' }], // 60px
        '7xl': ['4.5rem', { lineHeight: '1' }], // 72px
        '8xl': ['6rem', { lineHeight: '1' }], // 96px
        '9xl': ['8rem', { lineHeight: '1' }], // 128px
        // 扩展
        sl: ['0.875rem', { lineHeight: '1rem' }], // 14px, 16px
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
        spin360: {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' },
        },
        fadeOut: {
          from: { opacity: 1 },
          to: { opacity: 0 },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'spin-slow': 'spin 2s linear infinite',
        spin360: 'spin360 2s linear infinite',
      },
    },
  },
  plugins: [
    require('tailwindcss-animate'), // Tailwind 插件，tailwind 预置动画
    require('@tailwindcss/forms'), // Tailwind 插件，改进原生 HTML 表单元素的样式, 与 Tailwind 的设计风格保持一致
    require('tailwindcss'), // Tailwind CSS 插件
    require('autoprefixer'), // Autoprefixer 插件, 兼容性处理
    postcssPxToRem({
      rootValue: 16, // 根字体大小，通常为 16px
      unitPrecision: 5, // rem 精度，小数点后几位
      propList: ['*'], // 转换哪些属性的 px，['*'] 表示所有属性
      selectorBlackList: [], // 要忽略的选择器，可以在此处定义不转换的类或元素
      replace: true, // 直接替换 px 而不是保留 px 和 rem 两种单位
      mediaQuery: false, // 是否转换媒体查询中的 px
      minPixelValue: 0, // 需要转换的最小 px 值
    }),
  ],
} satisfies Config

export default config
