import { useEffect, useMemo, useState } from 'react'
import { Text, TextEnum } from '../text'
import { useTranslation } from 'react-i18next'
import clsx from 'clsx'
import { BookOpenIcon, CubeIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline'
import { useRouter } from 'next/router'
import { Namespace } from '@/i18n'
import Image from 'next/image'
export type TabName = 'home' | 'search' | 'explore' | 'generator'

export interface TabItem {
  icon: React.JSX.Element
  key: string
  title: string
  path: string
  beta?: boolean
}

const TabsComponent = ({ tabName }: { tabName?: TabName }) => {
  const { t } = useTranslation([Namespace.GLOBAL])
  const router = useRouter()

  const [activeTab, setActiveTab] = useState('home')

  useEffect(() => {
    if (tabName) {
      setActiveTab(tabName)
    } else {
      setActiveTab('')
    }
  }, [tabName])

  const tabs: TabItem[] = useMemo(() => {
    return [
      {
        icon: <BookOpenIcon className='h-4 w-4' />,
        key: 'home',
        title: t('header.tab1'),
        path: '/home',
      },
      {
        icon: <MagnifyingGlassIcon className='h-4 w-4' />,
        key: 'search',
        title: t('header.tab2'),
        path: '/basic-search',
      },
      {
        icon: <CubeIcon className='h-4 w-4' />,
        key: 'explore',
        title: t('header.tab3'),
        path: '/deep-explore/file-disk',
      },
      {
        icon: <BookOpenIcon className='h-4 w-4' />,
        key: 'generator',
        title: t('header.tab4'),
        path: '/report',
        beta: true,
      },
    ]
  }, [tabName, t])

  const handleClickTab = (item: TabItem) => {
    router.push(item.path)
  }

  return (
    <>
      {tabs.map((item) => {
        return (
          <div
            key={item.key}
            className={clsx(
              'flex cursor-pointer items-center rounded-md p-3',
              activeTab === item.key ? 'bg-primary-bg text-primary' : 'text-secondary-black-2',
            )}
            onClick={() => {
              handleClickTab(item)
            }}>
            {item.icon}
            <Text type={TextEnum.H6} className='ml-1 flex gap-x-2'>
              {item.title}
              {item.beta && <Image src='/images/bate.svg' alt='bate' width={40} height={14} />}
            </Text>
          </div>
        )
      })}
    </>
  )
}

export default TabsComponent
