import { fetchWithAuth } from '@/lib/fetch'
const apiUrl = process.env.NEXT_PUBLIC_API_URL

export enum GeneratorStatus {
  INIT = 'INIT', // 初始状态
  WAITING = 'WAITING', // 等待生成
  CREATING = 'CREATING', // 生成中
  FINISH = 'FINISH', // 生成结束
  CANCELED = 'CANCELED', // 取消生成文档
  FAILED = 'FAILED', // 失败
}
export enum ReportTypeEnum {
  EMPTY = 'EMPTY',
  BUSINESS_PLAN = 'BUSINESS_PLAN',
  COMPANY = 'COMPANY',
}
export interface ReportItemType {
  content?: string
  createdAt: number
  description: string
  reportId: string
  status: GeneratorStatus
  title: string
  userId: string
  type: ReportTypeEnum | null
  updatedAt: number
  documentGeneratorStatus?: GeneratorStatus | undefined
}

// 文档列表
export const reportListApi = async () => {
  const response = await fetchWithAuth<{ report: ReportItemType[] }>(`${apiUrl}/report/list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  })

  return response
}
// 获取文档详情
export const getReportApi = async (data: { reportId: string }) => {
  const response = await fetchWithAuth<{
    report: ReportItemType
  }>(`${apiUrl}/report/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
// 创建文档
export const reportCreateApi = async () => {
  const response = await fetchWithAuth<{
    reportId: string
  }>(`${apiUrl}/report/create`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  })

  return response
}
export const deleteReportApi = async (data: { reportId: string }) => {
  const response = await fetchWithAuth<{
    result: 'ok'
  }>(`${apiUrl}/report/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}

// 对话、pdf、图片 list
export const listSavedSourceApi = async (data: { pageSize?: number; lastPageKey?: string }) => {
  const response = await fetchWithAuth<{
    reportId: string
  }>(`${apiUrl}/report/listSource`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}

// 更新文档 report/update
export const updateDocumentApi = async (data: {
  reportId: string
  type?: ReportTypeEnum
  title?: string
  description?: string
}) => {
  const response = await fetchWithAuth<{
    result: string
  }>(`${apiUrl}/report/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}

// 获取报告模板 /report/getTemplate
export const getTemplateApi = async (data: { type: ReportTypeEnum }) => {
  const response = await fetchWithAuth<{
    content: string
  }>(`${apiUrl}/report/getTemplate`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
// 更新报告模板 /report/updateTemplate
export const updateTemplateApi = async (data: { type: ReportTypeEnum; content: string }) => {
  const response = await fetchWithAuth<{
    result: string
  }>(`${apiUrl}/report/updateTemplate`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
// 删除 source   report/deleteSource
export const deleteSourceApi = async (data: { id: string }) => {
  const response = await fetchWithAuth<{
    result: string
  }>(`${apiUrl}/report/deleteSource`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
// 文档生成 report/generate
export const reportGenerateApi = async (data: { reportId: string }) => {
  const response = await fetchWithAuth<{
    reportProcessId: string
  }>(`${apiUrl}/report/generate`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}

// 取消生成文档
export const reportCancelGenerateApi = async (data: { reportId: string }) => {
  const response = await fetchWithAuth<{
    result: string
  }>(`${apiUrl}/report/cancel`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
