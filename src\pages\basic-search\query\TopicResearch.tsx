import * as React from 'react'
import { Text, TextEnum } from '@/components/business/text'
import { TextTips } from '@/components/business/text-tips'
import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/router'
import { smartiesRoutes } from '@/routers'
import { Button } from '@/components/ui/button'
import { PaperAirplaneIcon } from '@heroicons/react/24/outline'
import { startQuest } from '@/api/startQuest'
import { PoTypeEnum, SearchModeEnum } from '@/types'
import { useEffect, useState } from 'react'
import CreditsModal from '@/components/business/credits-modal/CreditsModal'
import { Textinput } from '@/components/business/text-input'
import { checkCredits } from '../management'
import { useUserStore } from '@/store/userStore'
import SearchModeSelect from '@/components/business/searh-mode-select'

const TopicResearch = () => {
  const { t } = useTranslation(Namespace.BASIC_SEARCH)
  const router = useRouter()
  const user = useUserStore((state) => state.user)

  const [topic, setTopic] = React.useState<string>('')
  const [details, setDetails] = React.useState<string>('')

  const [disableSend, setDisableSend] = useState(false)
  const [sended, setSended] = useState(false)
  const [openCreditsModal, setOpenCreditsModal] = useState(false)
  const [searchMode, setSearchMode] = useState<SearchModeEnum>(SearchModeEnum.AUTO)

  useEffect(() => {
    if (topic.trim() && details.trim()) {
      setDisableSend(false)
    } else {
      setDisableSend(true)
    }
  }, [topic, details])

  const handleClickSend = async () => {
    if (!disableSend && !sended) {
      const checkRes = checkCredits(user)
      if (!checkRes) {
        setOpenCreditsModal(true)
        return false
      }
      setSended(true)
      try {
        const resp = await startQuest({
          message: [
            {
              key: 'topic',
              value: topic,
            },
            {
              key: 'details',
              value: details,
            },
          ],
          poType: PoTypeEnum.TOPIC,
          useReasoning: searchMode === SearchModeEnum.REASONING ? true : undefined,
        })
        if (resp.sessionId && resp.processId) {
          router.push({
            pathname: smartiesRoutes.basicSearch.chat(PoTypeEnum.TOPIC.toLocaleLowerCase()),
            query: {
              processId: resp.processId,
              sessionId: resp.sessionId,
            },
          })
        } else {
          setSended(false)
        }
      } catch (error) {
        // TODO toast 请求异常，请稍后
        console.error('query', error)
        setSended(false)
      }
    }
  }

  const handleQueryChange = (key: 'topic' | 'details', value: string) => {
    if (key === 'topic') {
      setTopic(value)
    }
    if (key === 'details') {
      setDetails(value)
    }
  }

  return (
    <div className='flex w-full min-w-[500px] max-w-[760px] flex-col gap-2.5'>
      <Text type={TextEnum.H3} className='mb-0.5'>
        {t('homePage.card.title_topic')}
      </Text>
      <TextTips title={t('topic.query1')} tips={t('topic.tips1')} />
      <Textinput
        onChange={(e) => {
          handleQueryChange('topic', e.target.value)
        }}
        placeholder={t('topic.query1Placeholder')}
        maxLength={5000}
        withIcon={false}
        value={topic}
        onEnter={handleClickSend}
      />
      <TextTips title={t('topic.query2')} tips={t('topic.tips2')} />
      <Textinput
        onChange={(e) => {
          handleQueryChange('details', e.target.value)
        }}
        placeholder={t('topic.query2Placeholder')}
        maxLength={5000}
        withIcon={false}
        value={details}
        onEnter={handleClickSend}
      />
      <div className='mt-5 flex items-center justify-between'>
        <SearchModeSelect
          searchMode={searchMode}
          onChangeSearchMode={(mode) => {
            setSearchMode(mode)
          }}
        />
        <div className='flex items-center'>
          <Text type={TextEnum.Body_small} className={'text-secondary-black-3'}>
            {t(`homePage.tip`)}
          </Text>
          <Button className='ml-2 px-14' onClick={handleClickSend} disabled={disableSend || sended}>
            <PaperAirplaneIcon className='mr-1 h-4 w-4' />
            {t('common.send')}
          </Button>
        </div>
      </div>
      <CreditsModal
        open={openCreditsModal}
        onClose={() => {
          setOpenCreditsModal(false)
        }}
      />
    </div>
  )
}

export default TopicResearch
