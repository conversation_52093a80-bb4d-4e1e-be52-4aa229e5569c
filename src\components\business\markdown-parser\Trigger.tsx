import { Toolt<PERSON>, Too<PERSON><PERSON>Content, <PERSON><PERSON>ipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Text, TextEnum } from '@/components/business/text'
import { memo, useCallback } from 'react'

const isValidUrl = (string: string | URL) => {
  try {
    new URL(string)
    return true
  } catch {
    return false
  }
}
const Trigger = memo(({ index, url }: { index: number; url: string }) => {
  const openUrl = useCallback(() => {
    if (isValidUrl(url)) {
      window.open(url, '_blank')
    }
  }, [url])

  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip>
        <TooltipTrigger>
          <div
            onClick={openUrl}
            aria-label={`Open link ${url}`}
            className='flex-center cursor-pointer text-primary'>
            [{index + 1}]
          </div>
        </TooltipTrigger>
        <TooltipContent
          side='bottom'
          align='start'
          sideOffset={0}
          avoidCollisions={false}
          className='z-[1000] rounded-sm'>
          <Text
            type={TextEnum.Body_medium}
            className='max-w-[400px] overflow-hidden text-ellipsis whitespace-nowrap text-left underline'>
            {url}
          </Text>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
})
Trigger.displayName = 'Trigger'
export default Trigger
