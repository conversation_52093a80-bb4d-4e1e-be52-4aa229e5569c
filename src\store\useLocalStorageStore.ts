import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
interface LocalStore {
  local: {
    [key: string]: string | undefined | null
  }
  setValue: (key: string, newValue: string) => void
  getValue: (key: string) => string | undefined
}
export const useLocalStorageStore = create<LocalStore>()(
  immer((set, get) => ({
    local: {},
    // 获取文档列表
    setValue: (key, newValue) => {
      localStorage.setItem(key, newValue) // 更新 localStorage
      set({
        local: {
          [key]: newValue,
        },
      })
    },
    getValue: (key: string) => {
      return get().local[key] || localStorage[key]
    },
  })),
)
