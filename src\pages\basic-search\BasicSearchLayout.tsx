import Layout from '@/components/business/layout/Layout'
import { MenuComponentHandles } from '@/components/business/layout/Menu'
import clsx from 'clsx'
import React from 'react'

interface BasicSearchLayoutProps {
  children: React.ReactNode
  className?: string | string[]
}

const BasicSearchLayout = React.forwardRef<MenuComponentHandles, BasicSearchLayoutProps>(
  ({ children, className }, ref) => {
    return (
      <Layout ref={ref} tabName='search'>
        <div className={clsx(className)}>{children}</div>
      </Layout>
    )
  },
)

BasicSearchLayout.displayName = 'BasicSearchLayout'
export default BasicSearchLayout
