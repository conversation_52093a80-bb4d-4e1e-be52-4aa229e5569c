import { Component, ErrorInfo, ReactNode } from 'react';
import { Tracking } from '@/lib/tracking'
import { TrackingEventType } from '@/lib/tracking/types'

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // 将错误日志上报给服务器
    console.error('Caught error: ', error, errorInfo);
    this.setState({ errorInfo })
    Tracking.trackEvent('ERROR_OCCURED', TrackingEventType.SUBMIT, {
      errorInfo: errorInfo,
      error: error,
    })
    // 可以在这里进行错误日志记录，例如发送到错误跟踪服务
  }

  render(): ReactNode {
    if (this.state.hasError) {
      return this.props.fallback;
    }
    return this.props.children;
  }
}

export default ErrorBoundary;