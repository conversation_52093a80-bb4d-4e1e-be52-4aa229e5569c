import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export const shareReportLink = (data: { reportId: string; action: 'share' | 'unshare' }) => {
  return fetchWithAuth(`${apiUrl}/report/share`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })
}

export const shareSessionLink = (data: { sessionId: string; action: 'share' | 'unshare' }) => {
  return fetchWithAuth(`${apiUrl}/session/share`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })
}

export const sharePdfLink = (data: { pdfId: string; action: 'share' | 'unshare' }) => {
  return fetchWithAuth(`${apiUrl}/pdf/share`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })
}
