import { SubscriptionEnum } from '@/types'

export const isBasicPlan = (currentPlan: SubscriptionEnum) => {
  return currentPlan === SubscriptionEnum.BASIC_MONTH || currentPlan === SubscriptionEnum.BASIC_YEAR
}

export const isProPlan = (currentPlan: SubscriptionEnum) => {
  return currentPlan === SubscriptionEnum.PRO_MONTH || currentPlan === SubscriptionEnum.PRO_YEAR
}

export const isMonthPlan = (currentPlan: SubscriptionEnum) => {
  return currentPlan === SubscriptionEnum.BASIC_MONTH || currentPlan === SubscriptionEnum.PRO_MONTH
}

export const isYearPlan = (currentPlan: SubscriptionEnum) => {
  return currentPlan === SubscriptionEnum.BASIC_YEAR || currentPlan === SubscriptionEnum.PRO_YEAR
}
