import Image from 'next/image'
import { ChatPreview } from '../chat/ChatPreview'
import { Text, TextEnum } from '@/components/business/text'
import { ArrowTopRightOnSquareIcon } from '@heroicons/react/24/outline'
import clsx from 'clsx'
export const MediaPreview = ({
  media,
  isShowChat = true,
  height = 'h-screen-minus-header',
  children,
  width = 'w-[calc(100vw-420px)]',
}) => {
  const handleClickImg = (url: string) => {
    if (url) {
      window.open(url, '_blank')
    }
  }
  return (
    <div className='flex'>
      <div className={clsx('border-r border-border px-4', width)}>
        <div className={clsx('relative w-full')}>
          <div className={clsx(height, 'relative')}>
            <Image
              src={media.url}
              alt='image-1'
              fill
              sizes='896px'
              className='rounded-sm'
              style={{
                objectFit: 'scale-down',
              }}
              priority
              quality={100}
            />
          </div>
        </div>
        {isShowChat && media?.siteUrl && (
          <div
            className='absolute bottom-3 left-10 flex h-8 max-w-[320px] cursor-pointer flex-row items-center gap-1 rounded-sm border border-border bg-card p-2'
            onClick={() => {
              handleClickImg(media?.siteUrl)
            }}>
            <Text
              type={TextEnum.Body_medium}
              className={'overflow-hidden text-ellipsis whitespace-nowrap'}>
              {media?.siteUrl}
            </Text>
            <ArrowTopRightOnSquareIcon className='h-4 w-4 shrink-0 text-secondary-foreground' />
          </div>
        )}
      </div>
      {isShowChat && <ChatPreview chat={media} className='w-[420px] p-4'></ChatPreview>}
      {children}
    </div>
  )
}
