import { useCallback, useContext, useEffect } from 'react'
import { I18nContext } from 'react-i18next'
import { SupportedLangs } from '.'
import { updateSetting } from '@/api/updateSetting'
import { getCookie, setLangToCookies } from '@/lib/cookie'
import { useUserStore } from '@/store/userStore'

export const LANGUAGEOPTIONS = [
  { label: 'English', value: 'en', beta: false },
  { label: '中文', value: 'zh', beta: false },
  { label: '日本語', value: 'ja', beta: true },
  { label: 'Français', value: 'fr', beta: true },
  { label: 'Deutsch', value: 'de', beta: true },
]
export const SUPPORTED_MAP = {
  en: '英文',
  zh: '中文',
  ja: '日语',
  fr: '法语',
  de: '德语',
}

export const useLanguage = ({ loggedIn = true }: { loggedIn: boolean }) => {
  const { i18n: instance } = useContext(I18nContext)
  const user = useUserStore((state) => state.user)
  const updateUser = useUserStore((state) => state.updateUser)

  const changeLanguage = useCallback(async (val: string) => {
    if (loggedIn) {
      const res = await updateSetting({
        language: val as SupportedLangs,
      })
      // update store 的 lang
      if (user)
        updateUser({
          ...user,
          setting: {
            language: res.language,
            isAutoAgent: user?.setting?.isAutoAgent,
          },
        })
      setLangToCookies(val)
    } else {
      setLangToCookies(val)
    }
    instance.changeLanguage(val)
  }, [])

  useEffect(() => {
    let lang = SupportedLangs.EN
    // getCookie('lang') 存在时，用户设置过，不存在，需要获取浏览器的语言
    if (getCookie('lang')) {
      lang = getCookie('lang') as SupportedLangs
    } else {
      // 获取浏览器的语言
      const browserLang = navigator.language || navigator.userLanguage
      if (browserLang) {
        switch (browserLang.toLowerCase()) {
          case 'zh-cn':
          case 'zh-hk':
          case 'zh-tw':
            lang = SupportedLangs.ZH
            break
          case 'ja':
            lang = SupportedLangs.JA
            break
          case 'fr':
            lang = SupportedLangs.FR
            break
          case 'de':
            lang = SupportedLangs.DE
            break
          default:
            lang = SupportedLangs.EN
            break
        }
      }
    }
    instance.changeLanguage(lang)
  }, [])

  return {
    language: instance.language,
    changeLanguage,
  }
}

export default useLanguage
