import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react'
import { getCookie, setCookie } from '@/lib/cookie'

type Theme = 'light' | 'dark'
interface ThemeContextProps {
  theme: Theme
  toggleTheme: () => void
}

const ThemeContext = createContext<ThemeContextProps | undefined>(undefined)

const LOCALSTORAGE_THEME_KEY = '__theme__'

export const ThemeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [theme, setTheme] = useState<Theme>((getCookie(LOCALSTORAGE_THEME_KEY) || 'light') as Theme)

  const toggleTheme = () => {
    setTheme((prevTheme) => {
      const currentTheme = prevTheme === 'light' ? 'dark' : 'light'
      setCookie(LOCALSTORAGE_THEME_KEY, currentTheme, {
        maxAge: 30 * 24 * 60 * 60,
        httpOnly: false,
      })
      return currentTheme
    })
  }

  useEffect(() => {
    const root = window.document.documentElement
    root.classList.remove('light', 'dark')
    root.classList.add(theme)
  }, [theme])

  return <ThemeContext.Provider value={{ theme, toggleTheme }}>{children}</ThemeContext.Provider>
}

export const useTheme = (): ThemeContextProps => {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}
