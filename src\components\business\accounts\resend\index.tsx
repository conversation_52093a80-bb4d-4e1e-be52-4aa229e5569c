import { ReactNode } from 'react'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import { Button } from '@/components/ui/button'
import { AccountTitle } from '../title'
import useIsMobileScreen from '@/components/ui/useIsMobile'
import clsx from 'clsx'

interface IResendEmailProps {
  email?: string
  suffix?: ReactNode
  onResend?: VoidFunction
  desc?: string
  disabled?: boolean
}

export const ResendEmail = (props: IResendEmailProps) => {
  const { t } = useTranslation([Namespace.GLOBAL, Namespace.LOGIN])

  const [isMobile] = useIsMobileScreen()

  return (
    <div className={clsx(isMobile ? 'w-11/12' : 'w-[420px]')} data-component='ResendEmail'>
      <AccountTitle
        title={t('login:check.title')}
        desc={
          props.desc
            ? props.desc
            : `${t('login:check.prefix')} ${props.email} ${t('login:check.suffix')}`
        }
      />
      <div className={clsx('flex-center flex-col', isMobile ? 'mt-2 w-full' : 'w-[420px]')}>
        <Button
          className='mb-5 w-full cursor-pointer rounded-sm bg-primary hover:bg-primary'
          onClick={props.onResend}
          disabled={props.disabled || false}>
          {t('login:check.resend')}
        </Button>
        {props.suffix}
      </div>
    </div>
  )
}
