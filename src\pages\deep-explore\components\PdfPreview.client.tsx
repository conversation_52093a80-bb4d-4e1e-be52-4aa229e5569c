import { PDFItemType } from '@/api/pdfs/getPdf'
import { Text, TextEnum } from '@/components/business/text'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Namespace } from '@/i18n'
import { cn } from '@/lib/utils'
import { ArrowDownTrayIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline'
import clsx from 'clsx'
import { throttle } from 'lodash'
import Image from 'next/image'
import React, { useEffect, useImperativeHandle, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Document, Page, pdfjs } from 'react-pdf'
import 'react-pdf/dist/esm/Page/AnnotationLayer.css'
import 'react-pdf/dist/esm/Page/TextLayer.css'
// import * as PDFJS from 'pdfjs-dist/build/pdf.worker.min.js'
// "pdfjs-dist": "3.11.174",

pdfjs.GlobalWorkerOptions.workerSrc =
  'https://d1ij1j35k83uw7.cloudfront.net/pdfjs/3.11.174/pdf.worker.min.js'

const PDFOptions = {
  cMapUrl: 'https://d1ij1j35k83uw7.cloudfront.net/pdfjs/3.11.174/cmaps/',
  cMapPacked: true,
  Worker: 'https://d1ij1j35k83uw7.cloudfront.net/pdfjs/3.11.174/pdf.worker.min.js',
}

export interface PdfPreviewProps {
  selectedPdf: PDFItemType | undefined
  header2Class?: string
  header3Class?: string
}
export interface PDFPreviewComponentHandles {
  updateInputPage: (page: number) => void
}

const MAX_PDF_PAGES = 200

const PdfPreview = React.forwardRef<PDFPreviewComponentHandles, PdfPreviewProps>(
  (
    {
      selectedPdf,
      header2Class = 'h-screen-minus-2-header',
      header3Class = 'h-screen-minus-3-header',
    },
    ref,
  ) => {
    const { t } = useTranslation(Namespace.DEEPEXPLORE)

    const [totalPages, setTotalPages] = useState<number>(
      Math.min(MAX_PDF_PAGES, selectedPdf?.totalPage ?? 0),
    )
    const pageRefs = useRef<(HTMLDivElement | null)[]>(Array(totalPages).fill(null))
    const [inputPage, setInputPage] = useState<number>(1)
    const isProgrammaticScroll = useRef(false)
    const inputRef = useRef<HTMLInputElement>(null)

    const [scale, setScale] = useState<number | undefined>(undefined)
    const [wid, setWid] = useState<number | undefined>(undefined)
    const [isAutoScale, setIsAutoScale] = useState<boolean>(true) // 控制是否自动缩放
    const containerRef = useRef<HTMLDivElement>(null)
    const [isDocumentReady, setIsDocumentReady] = useState(false)

    const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
      setIsDocumentReady(true)
      setTotalPages(numPages)
    }

    const handleLoadError = (e: Error) => {
      console.error('load error', e)
    }

    const scrollToView = (page: number) => {
      isProgrammaticScroll.current = true
      if (pageRefs.current[page - 1] && page !== null) {
        pageRefs.current[page - 1]?.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        })
      }
    }

    // 滚动时更新当前显示的页码
    useEffect(() => {
      const handleScroll = () => {
        // 如果是程序化滚动，不执行 handleScroll 逻辑
        if (isProgrammaticScroll.current) {
          return
        }
        if (pageRefs.current.length > 0 && containerRef.current) {
          const containerTop = containerRef.current.getBoundingClientRect().top

          // 找到最接近顶部的页面
          let closestPageIndex = -1
          let minDistance = Infinity

          pageRefs.current.forEach((pageRef, idx) => {
            if (pageRef) {
              const pageTop = pageRef.getBoundingClientRect().top
              const distance = Math.abs(pageTop - containerTop)
              if (distance < minDistance) {
                minDistance = distance
                closestPageIndex = idx
              }
            }
          })
          // 更新页码
          if (closestPageIndex !== -1 && closestPageIndex + 1 !== inputPage) {
            setInputPage(closestPageIndex + 1)
          }
        }
      }

      const throttledHandleScroll = throttle(handleScroll, 50) // 节流处理
      const containerEl = containerRef.current
      containerEl?.addEventListener('scroll', throttledHandleScroll)

      // 清理事件监听器
      return () => containerEl?.removeEventListener('scroll', handleScroll)
    }, [])

    // s3Url变换后 页面重置
    useEffect(() => {
      setInputPage(1)
    }, [selectedPdf?.s3Url])

    useEffect(() => {
      const handleResize = () => {
        if (containerRef.current) {
          const containerWidth = containerRef.current.offsetWidth
          const wid = containerWidth - 32 // 根据父容器宽度计算缩放比例
          setWid(wid > 0 ? wid : undefined)
        }
      }

      if (isAutoScale) {
        handleResize() // 初始化缩放

        const resizeObserver = new ResizeObserver(() => {
          handleResize()
        })

        if (containerRef.current) {
          resizeObserver.observe(containerRef.current)
        }

        return () => {
          if (containerRef.current) {
            resizeObserver.unobserve(containerRef.current)
          }
          resizeObserver.disconnect()
        }
      }
    }, [isAutoScale])

    useImperativeHandle(ref, () => {
      return {
        updateInputPage: (page: number) => {
          inputRef.current?.focus()
          setInputPage(page)
          scrollToView(Number(page))
        },
      }
    }, [])

    const handleScaleChange = (value: string) => {
      if (value === 'auto') {
        setIsAutoScale(true)
        setScale(undefined)
      } else {
        setIsAutoScale(false)
        setScale(Number(value))
      }
    }

    const handleClickDownLoad = async (s3Url: string) => {
      try {
        const response = await fetch(s3Url, {
          method: 'GET',
          mode: 'cors',
        })
        if (!response.ok) {
          throw new Error('Network response was not ok')
        }

        const blob = await response.blob()
        const link = document.createElement('a')
        link.href = window.URL.createObjectURL(blob)
        link.download = selectedPdf?.fileName || s3Url.split('/').pop() || 'download'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } catch (error) {
        console.error('Download failed:', error)
      }
    }
    return (
      <div className={cn('box-border border-none bg-background', header2Class)}>
        <div
          className='h-8 w-full bg-background'
          onMouseLeave={() => {
            isProgrammaticScroll.current = false
            inputRef.current?.blur()
          }}>
          <div className='relative flex flex-row items-center justify-center'>
            <ChevronLeftIcon
              className='my-2 mr-4 h-4 cursor-pointer'
              onClick={() => {
                inputRef.current?.focus()
                setInputPage(Math.max(inputPage - 1, 1))
                scrollToView(Math.max(inputPage - 1, 1))
              }}
            />
            <Input
              ref={inputRef}
              className={clsx(
                'my-1 mr-2 flex h-6 w-[50px] items-center justify-center border-none bg-card',
                'ring-offset-transparent focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-transparent focus-visible:ring-offset-0',
              )}
              value={inputPage}
              maxLength={999}
              onChange={(e) => {
                const onlyNums = e.target.value.replace(/[^0-9]/g, '')
                if (Number(onlyNums) <= MAX_PDF_PAGES) {
                  setInputPage(() => Number(onlyNums))
                  scrollToView(Number(onlyNums))
                } else {
                  setInputPage(() => MAX_PDF_PAGES)
                  scrollToView(MAX_PDF_PAGES)
                }
              }}
            />
            / {selectedPdf?.totalPage || 0}
            <ChevronRightIcon
              className='ml-4 h-4 cursor-pointer'
              onClick={() => {
                inputRef.current?.focus()
                setInputPage(Math.min(inputPage + 1, totalPages!))
                scrollToView(Math.min(inputPage + 1, totalPages!))
              }}
            />
            <div className='absolute right-0 mr-4 flex'>
              <div className='flex-center mr-2 h-6 cursor-pointer gap-1 rounded bg-card px-3 py-1'>
                <ArrowDownTrayIcon className='h-4 w-4' />
                <Text
                  type={TextEnum.Body_medium}
                  onClick={() => {
                    handleClickDownLoad(selectedPdf?.s3Url ?? '')
                  }}>
                  {t('download')}
                </Text>
              </div>
              <Select onValueChange={handleScaleChange} defaultValue={'auto'}>
                <SelectTrigger className='h-6 w-fit rounded border-none bg-card'>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup className='cursor-pointer'>
                    <SelectItem value='auto' className='cursor-pointer'>
                      {t('auto')}
                    </SelectItem>
                    <SelectItem value='0.5' className='cursor-pointer'>
                      50%
                    </SelectItem>
                    <SelectItem value='0.8' className='cursor-pointer'>
                      80%
                    </SelectItem>
                    <SelectItem value='1' className='cursor-pointer'>
                      100%
                    </SelectItem>
                    <SelectItem value='1.5' className='cursor-pointer'>
                      150%
                    </SelectItem>
                    <SelectItem value='2' className='cursor-pointer'>
                      200%
                    </SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        <div
          className={cn(
            'mx-4 flex h-screen-minus-3-header flex-col items-center gap-2 overflow-auto',
            header3Class,
          )}
          ref={containerRef}>
          <div
            style={{
              width: 'max-content',
              maxWidth: '100%',
            }}>
            <Document
              key={selectedPdf?.pdfId}
              loading={
                <div className='flex-center mt-[-100px] h-svh flex-col'>
                  <Image
                    src='/images/downloading.png'
                    alt=''
                    width={48}
                    height={48}
                    className='animate-spin360'
                  />
                  <Text type={TextEnum.Body_medium} className='mt-1 text-muted-foreground'>
                    {t('status.loading')}
                  </Text>
                </div>
              }
              error={
                <div className='flex-center mt-80 flex-grow flex-col'>
                  <Image src='/images/process_failed.png' alt='' width={48} height={48} />
                  <Text type={TextEnum.Body_medium} className='mt-1 text-muted-foreground'>
                    {t('status.previewFailed')}
                  </Text>
                </div>
              }
              options={PDFOptions}
              file={selectedPdf?.s3Url}
              onLoadSuccess={onDocumentLoadSuccess}
              onLoadError={handleLoadError}>
              {isDocumentReady &&
                [...Array(totalPages)]
                  .map((_, i) => i + 1)
                  .map((page, idx) => {
                    return (
                      <div
                        key={idx}
                        className='mb-2'
                        ref={(el) => {
                          pageRefs.current[idx] = el
                        }}>
                        <Page
                          pageNumber={page}
                          renderTextLayer={false}
                          scale={scale}
                          width={wid}
                          error={
                            <div className='flex-center mt-80 flex-grow flex-col'>
                              <Image
                                src='/images/process_failed.png'
                                alt=''
                                width={48}
                                height={48}
                              />
                              <Text
                                type={TextEnum.Body_medium}
                                className='mt-1 text-muted-foreground'>
                                {t('status.previewFailed')}
                              </Text>
                            </div>
                          }
                          loading={() => {
                            return (
                              <div className='flex-center mt-[-100px] h-svh flex-col'>
                                <Image
                                  src='/images/downloading.png'
                                  alt=''
                                  width={48}
                                  height={48}
                                  className='animate-spin360'
                                />
                                <Text
                                  type={TextEnum.Body_medium}
                                  className='mt-1 text-muted-foreground'>
                                  {t('status.loading')}
                                </Text>
                              </div>
                            )
                          }}
                        />
                      </div>
                    )
                  })}
            </Document>
          </div>
        </div>
      </div>
    )
  },
)

PdfPreview.displayName = 'PdfPreview'
export default PdfPreview
