import { fetchWithAuth } from '@/lib/fetch'
import { SupportedLangs } from '@/i18n'
import { AuthError } from './types'
import { getCookie } from '@/lib/cookie'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface RegisterEmailRequest {
  email: string
  password: string
  language: SupportedLangs
  referralCode?: string
}

export interface RegisterEmailResponse {
  userId: string
}

export const registerEmail = (data: RegisterEmailRequest) => {
  return fetchWithAuth<RegisterEmailResponse | AuthError>(`${apiUrl}/auth/registerEmail`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
      anonymousId: getCookie('anonymousId') || '',
    }),
  })
}
