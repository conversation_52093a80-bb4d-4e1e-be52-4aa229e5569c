@import 'px';
$default-font-family: var(--font-roboto);

@mixin base-font($size, $weight, $lineHeight) {
  font-size: px($size);
  font-weight: $weight;
  font-family: $default-font-family;
  line-height: px($lineHeight);
}

@mixin font-h1 {
  @include base-font(48, 900, 48);
}

@mixin font-h2 {
  @include base-font(32, 700, 48);
}

@mixin font-h3 {
  @include base-font(24, 500, 32);
}

@mixin font-h4 {
  @include base-font(18, 700, 28);
}

@mixin font-h5 {
  @include base-font(16, 700, 28);
}

@mixin font-h6 {
  @include base-font(14, 500, 20);
}

@mixin font-h7 {
  @include base-font(12, 500, 14);
}

@mixin font-title-big {
  @include base-font(40, 400, 48);
}

@mixin font-body-big {
  @include base-font(16, 400, 24);
}

@mixin font-body-medium {
  @include base-font(14, 400, 16);
}

@mixin font-body-small {
  @include base-font(12, 400, 16);
}

.h1 {
  @include font-h1;
}

.h2 {
  @include font-h2;
}

.h3 {
  @include font-h3;
}

.h4 {
  @include font-h4;
}

.h5 {
  @include font-h5;
}

.h6 {
  @include font-h6;
}

.h7 {
  @include font-h7;
}

.title_big {
  @include font-title-big;
}

.body_big {
  @include font-body-big;
}

.body_medium {
  @include font-body-medium;
}

.body_small {
  @include font-body-small;
}
