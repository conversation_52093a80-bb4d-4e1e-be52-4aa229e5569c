import { fetchWithAuth } from '@/lib/fetch'
import { ImageStatusEnum, MessageType, ModelEnum, PoTypeEnum, ProcessStatusEnum } from '@/types'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface GetInProgressTaskRequest {
  sessionId: string
}

export interface ProcessType {
  createdAt?: number
  updatedAt?: number
  inputMessage?: MessageType
  isFirst?: boolean
  model?: ModelEnum
  needSearch?: boolean
  poType?: PoTypeEnum
  processId: string
  sessionId?: string
  status: ProcessStatusEnum
  userId?: string
  saveImage?: ImageStatusEnum
  downloadPdf?: string
}
export interface GetInProgressTaskResponse {
  process?: ProcessType | null
}

export const getInProgressTask = async (
  data: GetInProgressTaskRequest,
): Promise<GetInProgressTaskResponse> => {
  const response = await fetchWithAuth<GetInProgressTaskResponse>(`${apiUrl}/process/inprogress`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
