import { Modal } from '@/components/business/modal'
import { TextEnum, Text } from '@/components/business/text'
import { Button } from '@/components/ui/button'
import { Namespace } from '@/i18n'
import { smartiesRoutes } from '@/routers'
import { useRouter } from 'next/router'
import { Trans, useTranslation } from 'react-i18next'

export interface CreditsModalProps {
  open: boolean
  onClose: () => void
}
const CreditsModal: React.FC<CreditsModalProps> = ({ open, onClose }: CreditsModalProps) => {
  const { t } = useTranslation(Namespace.GLOBAL)
  const router = useRouter()

  const handleCloseModal = () => {
    if (onClose) onClose()
  }
  const handleConfirm = () => {
    router.push(smartiesRoutes.pricing)
  }

  return (
    <>
      <Modal open={open} className='w-[420px] break-all p-6' onClose={onClose}>
        <div>
          <Text type={TextEnum.H4}> {t('global:creditModal.title')}</Text>
          <Text type={TextEnum.Body_big} className='mt-4'>
            <Trans
              t={t}
              i18nKey='global:creditModal.content'
              components={{ email: <span className='text-primary' /> }}
            />
          </Text>
        </div>
        <div className='mt-5 flex w-full justify-end gap-2'>
          <Button onClick={handleCloseModal} variant='secondary'>
            {t('global:button.cancel')}
          </Button>

          <Button onClick={handleConfirm}>{t('global:button.viewPlans')}</Button>
        </div>
      </Modal>
    </>
  )
}

export default CreditsModal
