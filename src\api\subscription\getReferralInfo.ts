import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface GetReferralInfoResponse {
  referralCode: string
  creditEarned: number
  referralsCount: number
}

export const getReferralInfo = async (): Promise<GetReferralInfoResponse> => {
  const response = await fetchWithAuth<GetReferralInfoResponse>(`${apiUrl}/user/getReferralInfo`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  })

  return response
}
