import { ParentSessionType, PDFItemType } from '@/api/pdfs/getPdf'
import { TextEnum, Text } from '@/components/business/text'
import { smartiesRoutes } from '@/routers'
import { ArrowTopRightOnSquareIcon, SlashIcon } from '@heroicons/react/24/outline'
import router from 'next/router'

interface HeaderProps {
  parentSession?: ParentSessionType
  selectedPdf?: PDFItemType
}

export const Header: React.FC<HeaderProps> = ({ parentSession, selectedPdf }) => {
  const handleClickUrl = () => {
    if (selectedPdf?.url) {
      window.open(selectedPdf?.url, '_blank')
    }
  }

  const handleOpenSession = () => {
    if (parentSession?.sessionId && parentSession.poType) {
      router.push({
        pathname: smartiesRoutes.basicSearch.chat((parentSession.poType ?? '').toLocaleLowerCase()),
        query: {
          sessionId: parentSession?.sessionId,
        },
      })
    }
  }
  return (
    <div className='flex h-11 w-full items-center gap-2.5 pl-4'>
      <Text
        onClick={handleOpenSession}
        type={TextEnum.Body_medium}
        className='max-w-[112px] cursor-pointer overflow-hidden text-ellipsis whitespace-nowrap text-secondary-black-3'>
        {parentSession?.title}
      </Text>
      <SlashIcon className='h-4 shrink-0 text-secondary-black-3' />
      <Text
        onClick={handleClickUrl}
        type={TextEnum.Body_medium}
        className='max-w-[376px] cursor-pointer overflow-hidden text-ellipsis whitespace-nowrap'>
        {selectedPdf?.title}
      </Text>
      <ArrowTopRightOnSquareIcon
        className='h-4 w-4 shrink-0 cursor-pointer text-secondary-foreground'
        onClick={handleClickUrl}
      />
    </div>
  )
}
