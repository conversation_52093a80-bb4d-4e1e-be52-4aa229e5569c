/* eslint-disable @typescript-eslint/no-explicit-any */
import { useTranslation } from 'react-i18next'

import { MessageRoleEnum } from '@/api/getSession'
import { saveChatSource, saveMediaSource, savePdfSource } from '@/api/report/saveSource'
import { startQuest } from '@/api/startQuest'
import { updateSetting } from '@/api/updateSetting'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { EventBus$ } from '@/eventbus'
import { Namespace } from '@/i18n'
import { smartiesRoutes } from '@/routers'
import { useUserStore } from '@/store/userStore'
import { MessageType, ModelEnum } from '@/types/index'
import { BookmarkIcon, MagnifyingGlassIcon, PowerIcon } from '@heroicons/react/24/outline'
import { useRouter } from 'next/router'
import CompanySearchIcon from 'public/images/companySearch.svg'
import MarketSearchIcon from 'public/images/marketSearch.svg'
import { toast } from 'sonner'
import { useCloseOutside } from './useCloseOutside'
import { useMouseUp } from './useMouseUp'
import { useResize } from './useResize'
import { useScroll } from './useScroll'

export const SelectDropdown = ({
  messages,
  dropdownWidth = 230,
  sourceId,
  type = 'chat',
  showListKey = ['saveToDoc', 'generalSearch', 'marketSearch', 'companySearch', 'close'],
  onSearchLoading,
}: any) => {
  const router = useRouter()
  const { t } = useTranslation(Namespace.GLOBAL)
  const user = useUserStore((state) => state.user)
  const updateUser = useUserStore((state) => state.updateUser)
  // 点击弹出框外部 关闭弹出
  const { isOpen, setIsOpen, dropdownRef } = useCloseOutside()
  // 监听鼠标 弹起事件,获取选中内容
  const { dropdownPosition, selectedText, setDropdownPosition, getElement }: any = useMouseUp({
    setIsOpen,
    dropdownWidth,
  })
  // 监听页面尺寸变化，动态设置弹出位置
  useResize(setDropdownPosition, getElement, dropdownWidth)
  // 监听页面滚动，动态设置弹出位置
  useScroll(setDropdownPosition, getElement, dropdownWidth)
  // 划线保存接口
  const onSaveToDoc = {
    chat: async () => {
      return await saveChatSource(
        [
          {
            content: selectedText.replace(/\[\d+\]/g, '').trim(),
            role: MessageRoleEnum.ASSISTANT,
          },
        ],
        sourceId,
        false,
      )
    },
    media: async () => {
      return await saveMediaSource(
        [
          {
            content: selectedText.replace(/\[\d+\]/g, '').trim(),
            role: MessageRoleEnum.ASSISTANT,
          },
        ],
        sourceId,
        false,
      )
    },
    pdf: async () => {
      return await savePdfSource(
        [
          {
            content: selectedText.replace(/\[\d+\]/g, '').trim(),
            role: MessageRoleEnum.ASSISTANT,
          },
        ],
        sourceId,
        false,
      )
    },
  }
  const onSearchEvent = async (item: any) => {
    if (item.key === 'close') {
      const res = await updateSetting({
        disableToolbar: true,
      })
      if (user)
        updateUser({
          ...user,
          setting: {
            ...user.setting,
            disableToolbar: res.disableToolbar,
          },
        })
      return setIsOpen(false)
    }
    const poType: any = item.poType
    const queryKey = item.queryKey
    const queryText = selectedText.replace(/\[\d+\]/g, '')
    if (item.key === 'saveToDoc') {
      try {
        await onSaveToDoc[type]()
        toast.success(t('toast.success'), {
          duration: 3000,
        })
      } catch (error) {
        console.error('Save to doc error:', error)
        onSearchLoading(false)
      }
      // 保存成功后关闭划线工具
      setIsOpen(false)
      return
    }

    const message = [{ key: queryKey, value: queryText }] as MessageType
    if (item.key === 'marketSearch') {
      const generalSearchContent = messages[0]?.content[1]
      if (generalSearchContent && generalSearchContent.key) {
        message.push({ ...messages[0]?.content[1] })
      }
    }
    startQuest({
      poType,
      model: ModelEnum.CLAUDE,
      message,
      inline: true,
    })
      .then((res) => {
        const { sessionId } = res
        setTimeout(() => {
          EventBus$.emit('update-session', poType)
        }, 500)
        router.push({
          pathname: smartiesRoutes.basicSearch.chat(item.poType.toLowerCase()),
          query: {
            sessionId,
          },
        })
      })
      .catch(() => {
        onSearchLoading(false)
      })
  }
  const drawList = [
    {
      key: 'saveToDoc',
      isOnlyShowTitle: true,
      render: (item) => (
        <BookmarkIcon
          onClick={() => {
            onSearchEvent(item)
          }}
          className='mr-2 h-7 w-7 cursor-pointer rounded p-0.5 hover:bg-secondary-hover hover:text-primary'
        />
      ),
    },
    {
      key: 'generalSearch',
      isOnlyShowTitle: false,
      poType: 'GENERAL',
      queryKey: 'query',
      render: (item: any) => {
        return (
          <MagnifyingGlassIcon
            onClick={() => {
              onSearchEvent(item)
            }}
            className='mr-2 h-7 w-7 cursor-pointer rounded p-0.5 hover:bg-secondary-hover hover:text-primary'></MagnifyingGlassIcon>
        )
      },
    },
    {
      key: 'marketSearch',
      isOnlyShowTitle: false,
      poType: 'MARKET',
      queryKey: 'description',
      render: (item: any) => {
        return (
          <div
            className='relative mr-2 h-7 w-7 cursor-pointer rounded hover:bg-secondary-hover hover:text-primary'
            onClick={() => {
              onSearchEvent(item)
            }}>
            <MarketSearchIcon className='absolute -top-[2px] right-[-1px]' />
          </div>
        )
      },
    },
    {
      key: 'companySearch',
      isOnlyShowTitle: false,
      poType: 'COMPANY',
      queryKey: 'companyName',
      render: (item: any) => {
        return (
          <div
            className='relative mr-2 h-7 w-7 cursor-pointer rounded hover:bg-secondary-hover hover:text-primary'
            onClick={() => {
              onSearchEvent(item)
            }}>
            <CompanySearchIcon className='absolute -top-[2px] right-[-1px]' />
          </div>
        )
      },
    },
    {
      key: 'close',
      isOnlyShowTitle: true,
      render: (item: any) => {
        return (
          <div className='flex'>
            <span className='leading-8 text-border'>|</span>
            <PowerIcon
              onClick={() => {
                onSearchEvent(item)
              }}
              className='ml-2 h-7 w-7 cursor-pointer rounded p-0.5 hover:bg-secondary-hover hover:text-primary'></PowerIcon>
          </div>
        )
      },
    },
  ].filter((d) => showListKey.includes(d.key))
  return (
    <>
      {isOpen && !user?.setting.disableToolbar ? (
        <div
          className='absolute z-0 animate-accordion-down'
          style={dropdownPosition}
          ref={dropdownRef}>
          <div className='flex rounded-md border border-solid border-border bg-card px-3 py-1.5 shadow-md'>
            {drawList.map((item) => {
              return (
                <TooltipProvider delayDuration={0} key={item.key}>
                  <Tooltip>
                    <TooltipTrigger asChild>{item.render(item)}</TooltipTrigger>
                    <TooltipContent
                      sideOffset={8}
                      className='text rounded-sm border-none bg-tooltip p-2'>
                      <div className='text-tooltip-foreground'>
                        <p>{t(`${item.key}.title`)}</p>
                        {!item.isOnlyShowTitle && <p>{t(`${item.key}.desc`)}</p>}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )
            })}
          </div>
        </div>
      ) : null}
    </>
  )
}

export default SelectDropdown
