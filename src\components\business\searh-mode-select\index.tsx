import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import AutoIcon from 'public/images/auto.svg'
import ReasoningIcon from 'public/images/reasoning.svg'
import { Text, TextEnum } from '../text'
import { SearchModeEnum } from '@/types'

const SearchModeSelect = ({
  searchMode = SearchModeEnum.AUTO,
  onChangeSearchMode,
}: {
  searchMode: SearchModeEnum
  onChangeSearchMode: (mode: SearchModeEnum) => void
}) => {
  const { t } = useTranslation(Namespace.BASIC_SEARCH)
  const searchModeOptions = [
    {
      value: SearchModeEnum.AUTO,
      tag: (
        <div className='flex items-center'>
          <AutoIcon />
          <div className='ml-2'>
            <Text type={TextEnum.Body_medium}>{t('homePage.auto')}</Text>
          </div>
        </div>
      ),
      icon: <AutoIcon />,
    },
    {
      value: SearchModeEnum.REASONING,
      tag: (
        <div className='flex items-center'>
          <ReasoningIcon />
          <div className='ml-2'>
            <Text type={TextEnum.Body_medium}>{t('homePage.reasoning')}</Text>
          </div>
        </div>
      ),
      icon: <ReasoningIcon />,
    },
  ]

  return (
    <div>
      <Select
        defaultValue={searchMode}
        onValueChange={(value: SearchModeEnum) => {
          onChangeSearchMode(value)
        }}>
        <SelectTrigger className='flex w-auto cursor-pointer items-center justify-center rounded-sm border-none p-0 text-primary hover:text-[#7C8EFD]'>
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {searchModeOptions.map((option) => (
            <SelectItem key={option.value} value={option.value} className='text-secondary-black-2'>
              {option.tag}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}

SearchModeSelect.displayName = 'Searchmodeselect'
export default SearchModeSelect
