import { MessageRoleEnum } from '@/api/getSession'
import clsx from 'clsx'
import Image from 'next/image'

const SmartiesAvatar = ({
  type,
  className,
}: {
  type: MessageRoleEnum
  className?: string | string[]
}) => {
  return (
    <Image
      src={
        type === MessageRoleEnum.USER ? '/images/default_avatar.svg' : '/images/system_avatar.svg'
      }
      width={32}
      height={32}
      className={clsx('rounded-lg', className)}
      alt={'avatar'}
    />
  )
}
export default SmartiesAvatar
