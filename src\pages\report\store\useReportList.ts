import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { reportListApi, ReportItemType } from '@/api/document/index'
interface ReportListStore {
  reportList: ReportItemType[]
  getReportList: () => void
  updateReportList: (reportList: ReportItemType[]) => void
}
export const useReportList = create<ReportListStore>()(
  immer((set, get) => ({
    reportList: [],
    // 获取文档列表
    getReportList: async () => {
      try {
        const res = await reportListApi()
        get().updateReportList(res)
        return res
        // set({ isLoadListing: false })
      } catch (error) {
        console.error('获取报告列表失败', error)
        // set({ isLoadListing: false })
      }
    },
    // 更新文档列表
    updateReportList: (reportList: ReportItemType[]) => {
      set({ reportList })
    },
  })),
)
