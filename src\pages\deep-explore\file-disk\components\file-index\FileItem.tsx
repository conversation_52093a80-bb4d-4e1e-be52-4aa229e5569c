import { Text, TextEnum } from '@/components/business/text'
import { Checkbox } from '@/components/ui/checkbox'
import { useTime } from '@/lib/hooks/useTime'
import { smartiesRoutes } from '@/routers/index'
import clsx from 'clsx'
import Image from 'next/image'
import { useRouter } from 'next/router'
import { useState } from 'react'
import { FileTypeEnum } from '../../const'
interface FileItemProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  item: any
  toggleSelectItem: (checked: boolean, id: string) => void
}

export const FileItem: React.FC<FileItemProps> = ({ item, toggleSelectItem }) => {
  const formatTimestamp = useTime()
  const router = useRouter()
  const [loadFaild, setLoadFaild] = useState(false)
  const [showCheckbox, setShowChecked] = useState(false)

  const onClickItem = () => {
    router.push({
      pathname: smartiesRoutes.deepExplore.preview,
      query: {
        fileId: item.fileId,
      },
    })
  }

  return (
    <div
      className='relative h-[182px] w-[140px] cursor-pointer text-center'
      onClick={onClickItem}
      onMouseEnter={() => setShowChecked(true)}
      onMouseLeave={() => setShowChecked(false)}>
      <div
        className={clsx(
          'h-28 w-[140px] rounded-sm hover:bg-background',
          item.checked || showCheckbox ? 'bg-background' : '',
        )}>
        <div className='relative m-auto flex h-28 w-[103px] items-center justify-center'>
          {!loadFaild ? (
            <Image
              src={item.thumbnailS3Url}
              alt='image-1'
              fill
              sizes='(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 103px'
              className='rounded-sm'
              loading='lazy'
              style={{
                objectFit: 'scale-down',
              }}
              onError={() => {
                setLoadFaild(true)
              }}
            />
          ) : (
            <>
              {item.fileType === FileTypeEnum.PDF ? (
                <Image src='/images/pdf_icon.svg' alt='Pdf load failed' width={40} height={40} />
              ) : (
                <Image
                  src='/images/image_load_failed.svg'
                  alt='Image load failed'
                  width={32}
                  height={32}
                />
              )}
            </>
          )}
        </div>
        {(showCheckbox || item.checked) && (
          <Checkbox
            onClick={(e) => {
              e.stopPropagation()
            }}
            checked={item.checked}
            onCheckedChange={(checked) => toggleSelectItem(checked, item)}
            className='absolute left-1 top-1 h-5 w-5 rounded-lg border-2 border-border'
          />
        )}
      </div>

      <Text
        type={TextEnum.Body_medium}
        className='mt-2 max-h-8 overflow-hidden text-ellipsis break-words'>
        {item.fileName}
      </Text>
      <Text type={TextEnum.Body_small} className='mt-2 text-center text-muted-foreground'>
        {formatTimestamp(item.createdAt)}
      </Text>
    </div>
  )
}
