import React, { useState } from 'react'
import { Text, TextEnum } from '@/components/business/text'
import { QuestionMarkCircleIcon } from '@heroicons/react/24/outline'

export interface TextTipsProps {
  size?: 'big' | 'small'
  title: string
  tips: string
}

const TextTips = (props: TextTipsProps) => {
  const { size = 'small' } = props
  const [visible, setVisible] = useState(false)

  return (
    <div className='flex flex-col'>
      <div className='flex'>
        <Text
          type={size === 'small' ? TextEnum.H5 : TextEnum.H3}
          className={size === 'small' ? '' : 'mb-0.5'}>
          {props.title}
        </Text>
        <QuestionMarkCircleIcon
          className='ml-1 cursor-pointer'
          width={20}
          onClick={() => setVisible((prev) => !prev)}
        />
      </div>
      {visible ? (
        <Text type={TextEnum.Body_small} className='mt-1 text-secondary-black-3'>
          {props.tips}
        </Text>
      ) : null}
    </div>
  )
}

TextTips.displayName = 'TextTips'

export { TextTips }
