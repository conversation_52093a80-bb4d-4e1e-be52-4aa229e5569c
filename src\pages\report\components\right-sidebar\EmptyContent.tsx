import useLanguage from '@/i18n/useLanguage'
import Image from 'next/image'
import { useMemo } from 'react'

const EmptyConent = () => {
  const { language } = useLanguage({ loggedIn: true })

  const imageUrl = useMemo(() => {
    switch (language) {
      case 'zh':
        return '/images/report_direct_zh.png'
      case 'en':
        return '/images/report_direct_en.png'
      case 'ja':
        return '/images/report_direct_ja.png'
      case 'fr':
        return '/images/report_direct_fr.png'
      case 'de':
        return '/images/report_direct_de.png'
      default:
        return '/images/report_direct_en.png'
    }
  }, [language])

  return (
    <div className='flex-center w-full justify-center'>
      <Image
        src={imageUrl}
        width={332}
        height={618}
        alt='Report using direction image'
        className='mt-7'
      />
    </div>
  )
}

export default EmptyConent
