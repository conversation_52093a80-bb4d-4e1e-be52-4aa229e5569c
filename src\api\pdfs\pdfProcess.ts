import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export enum PDFProcessStatusEnum {
  IN_PROGRESS = 'IN_PROGRESS',
  FINISH = 'FINISH',
  FAILED = 'FAILED',
  CANCELED = 'CANCELED',
}

export interface PdfProcessItem {
  pdfProcessId: string
  pdfSessionId: string
  pdfId: string
  status: PDFProcessStatusEnum
  message: string
  result?: string
}

export interface PdfProcessRequest {
  pdfProcessId: string
}

export type PdfProcessResponse = {
  pdfProcess: PdfProcessItem
}

export const pdfProcess = async (data: PdfProcessRequest): Promise<PdfProcessResponse> => {
  const response = await fetchWithAuth<PdfProcessResponse>(`${apiUrl}/pdf/pdfProcess`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
