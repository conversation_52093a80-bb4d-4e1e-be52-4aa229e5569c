import { PropsWithChildren } from 'react'
import Image from 'next/image'
import { useTranslation } from 'react-i18next'
import useLanguage, { LANGUAGEOPTIONS } from '@/i18n/useLanguage'
import { Namespace } from '@/i18n'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../ui/select'
import { Text, TextEnum } from '../../text'
import clsx from 'clsx'
import useIsMobileScreen from '@/components/ui/useIsMobile'

interface IAccountLayoutProps {
  isIframe?: boolean
}

export const AccountLayout = (props: PropsWithChildren<IAccountLayoutProps>) => {
  const { children, isIframe = false } = props
  const { t } = useTranslation([Namespace.LOGIN])
  const { language, changeLanguage } = useLanguage({ loggedIn: false })
  const handleChangeLang = (value: string) => {
    changeLanguage(value)
  }

  const [isMobile, isSmallScreen] = useIsMobileScreen()

  return (
    <div
      className={clsx(
        'bg-image-custom relative flex h-screen w-screen flex-row justify-between gap-20 bg-black-1 bg-[url("/images/login_bg.webp")]',
        isIframe && 'w-[480px]',
      )}
      data-component='AccountLayout'>
      {!isIframe && (
        <div className={clsx('flex-v-center absolute left-16', isIframe ? 'top-4' : 'top-16')}>
          <Image
            onClick={() => window.open('https://ai-smarties.com/', '_self')}
            className='mr-3 cursor-pointer'
            src='/images/logo.svg'
            alt='Logo'
            width={56}
            height={56}
          />
          <Text type={TextEnum.H2} className='font-sans text-white'>
            {t('login:logoText')}
          </Text>
        </div>
      )}

      <div className='absolute right-6 top-6 flex w-full justify-end'>
        <Select onValueChange={handleChangeLang} defaultValue={language} dir='ltr'>
          <SelectTrigger className='h-8 w-[140px] !border-none bg-[#19191C] text-white ring-offset-[#19191C] focus:ring-0 focus:ring-[#19191C]'>
            <SelectValue />
          </SelectTrigger>
          <SelectContent className='w-[180px] rounded-sm border-none bg-[#19191C] text-white'>
            <SelectGroup>
              {LANGUAGEOPTIONS.map((lang, index) => (
                <SelectItem key={index} value={lang.value} className='cursor-pointer px-4'>
                  <Text type={TextEnum.H6} className='flex items-center'>
                    <span className='mr-1'>{lang.label}</span>
                    {lang.beta && (
                      <Image src='/images/beta_drak.svg' alt='bate' width={36} height={14} />
                    )}
                  </Text>
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
      {!isIframe && !isMobile && !isSmallScreen && (
        <div className='flex-v-center min-w-[600px] max-w-[1000px] flex-1 pl-16 text-6xl font-black leading-normal text-white'>
          {t('login:sologn')}
        </div>
      )}

      <div
        className={clsx(
          'flex-center shrink-0 flex-col',
          isIframe
            ? 'w-[480px] pr-8'
            : isMobile || isSmallScreen
              ? 'w-full overflow-hidden pr-0'
              : 'w-[520px] pr-[100px]',
        )}>
        {children}
      </div>
    </div>
  )
}
