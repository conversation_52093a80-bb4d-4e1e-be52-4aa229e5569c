import { DocumentIcon, ArrowTopRightOnSquareIcon, BookmarkIcon } from '@heroicons/react/24/outline'
import { Text, TextEnum } from '../../text'
import Image from 'next/image'
import { useState } from 'react'
import clsx from 'clsx'
import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import { smartiesRoutes } from '@/routers'
import router from 'next/router'
import { PDFItemType } from '@/api/pdfs/getPdf'

export interface PDFSectionProps {
  pdfs?: PDFItemType[]
  onSave?: (urls: [string]) => void
  processId: string
}

export const PDFSection: React.FC<PDFSectionProps> = ({ pdfs, onSave, processId }) => {
  const { t } = useTranslation(Namespace.BASIC_SEARCH)

  const [currentHoverIndex, setCurrentHoverIndex] = useState<number>(-1)
  const handleOpenUrl = (pdfId: string) => {
    router.push({
      pathname: smartiesRoutes.deepExplore.home,
      query: {
        processId: processId,
        pdfId,
      },
    })
  }

  const handleSaveUrl = (url: string) => {
    if (url) {
      //
    }
  }

  const handleMouseLeave = (index: number) => {
    if (currentHoverIndex === index) {
      setCurrentHoverIndex(-1)
    }
  }
  const handleMouseEnter = (index: number) => {
    setCurrentHoverIndex(index)
  }
  return (
    <>
      {pdfs === undefined || pdfs.length === 0 ? null : (
        <div className='rounded-md bg-secondary'>
          <div className='flex-v-center mt-1 justify-between px-3 py-4'>
            <div className='flex-center gap-2 text-foreground'>
              <DocumentIcon className='h-4 w-4' />
              <Text type={TextEnum.Body_medium}> {t('chat.rightSide.PDF')}</Text>
            </div>
            {onSave && <Text type={TextEnum.Body_medium}>{t('chat.rightSide.save')}</Text>}
          </div>
          <div className='hide-scrollbar max-h-[400px] overflow-y-scroll'>
            {pdfs?.map((item, index) => (
              <div
                key={index}
                className='flex-v-center relative mx-3 mb-2 gap-2 rounded-sm bg-card p-2'
                onMouseEnter={() => {
                  handleMouseEnter(index)
                }}>
                <Image
                  // src={item.favicon ? item.favicon : '/images/pdf_icon.svg'}
                  src={'/images/pdf_icon.svg'}
                  alt='pdf icon'
                  width={32}
                  height={32}
                />

                <div className='flex-1 overflow-hidden'>
                  <Text
                    type={TextEnum.H6}
                    className='overflow-hidden text-ellipsis whitespace-nowrap'>
                    {item.title}
                  </Text>
                  {item.snippet && (
                    <Text
                      type={TextEnum.Body_small}
                      className='h-8 overflow-hidden text-ellipsis break-words'>
                      {item.snippet}
                    </Text>
                  )}
                </div>
                {currentHoverIndex === index && (
                  <div
                    className='flex-center absolute left-0 top-0 h-full w-full cursor-pointer rounded-sm bg-card-95'
                    onMouseLeave={() => {
                      handleMouseLeave(index)
                    }}>
                    <div
                      className='flex-center h-full w-full px-5'
                      asm-tracking='TEST_CLICK_PDF_VIEW:CLICK'
                      onClick={() => {
                        handleOpenUrl(item.pdfId)
                      }}>
                      <ArrowTopRightOnSquareIcon className='mb-1 h-4 w-4' />
                      <Text
                        type={TextEnum.Body_medium}
                        className={clsx('ml-1', onSave ? 'mr-10' : '')}>
                        {t('chat.rightSide.open')}
                      </Text>
                    </div>

                    {onSave && (
                      <>
                        <BookmarkIcon
                          className='mb-1 h-4 w-4'
                          onClick={() => {
                            handleSaveUrl(item.url)
                          }}
                        />
                        <Text
                          type={TextEnum.Body_medium}
                          className='ml-1'
                          onClick={() => {
                            handleSaveUrl(item.url)
                          }}>
                          {t('chat.rightSide.saveToDoc')}
                        </Text>
                      </>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </>
  )
}

export default PDFSection
