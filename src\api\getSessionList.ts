import { fetchWithAuth } from '@/lib/fetch'
import { PoTypeEnum } from '@/types'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface SessionItem {
  sessionId: string
  title: string
  poType: PoTypeEnum
}
export interface GetSessionListRequest {
  poType: PoTypeEnum
  pageSize?: number
  lastPageKey?: string
}

export interface GetSessionListResponse {
  items: SessionItem[]
  pageKey?: string | null
}

export const getSessionList = async (
  data: GetSessionListRequest,
): Promise<GetSessionListResponse> => {
  const response = await fetchWithAuth<GetSessionListResponse>(`${apiUrl}/session/list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
