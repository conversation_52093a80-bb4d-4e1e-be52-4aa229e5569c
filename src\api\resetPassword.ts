import { fetchWithAuth } from '@/lib/fetch'
import { AuthError } from './types'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface ResetPasswordRequest {
  code: string
  newPassword: string
}

export interface ResetPasswordResponse {
  accessToken: string
}

export const resetPassword = (data: ResetPasswordRequest) => {
  return fetchWithAuth<ResetPasswordResponse | AuthError>(`${apiUrl}/auth/resetPassword`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })
}
