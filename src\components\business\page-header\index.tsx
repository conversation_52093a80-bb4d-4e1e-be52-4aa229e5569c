import React, { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import Image from 'next/image'
import clsx from 'clsx'
import {
  ChevronDownIcon,
  ArrowRightStartOnRectangleIcon,
  UserGroupIcon,
  SunIcon,
  LanguageIcon,
  CheckIcon,
  UserCircleIcon,
  UnderlineIcon,
  ChatBubbleLeftRightIcon,
} from '@heroicons/react/24/outline'
import { Namespace } from '@/i18n'
import { useLanguage, LANGUAGEOPTIONS } from '@/i18n/useLanguage'
import { useTheme } from '@/context/ThemeContext'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '../../ui/dropdown-menu'
import { Switch } from '../../ui/switch'
import { Avatar, AvatarFallback } from '../../ui/avatar'
import { Text, TextEnum } from '../text'
import { TopBar } from '../layout/TopBar'
import { useRouter } from 'next/router'
import { signOut } from 'next-auth/react'
import ContactUsModal from '../contact-us/ContactUsModal'
import { smartiesRoutes } from '@/routers'
import { useUserStore } from '@/store/userStore'
import { updateSetting } from '@/api/updateSetting'

import { SubscriptionEnum } from '@/types'
import { Credits } from './Credits'
import TabsComponent, { TabName } from './TabsComponent'

export interface PageHeaderProps {
  classes?: string | string[]
  tabName?: TabName
  showLogo?: boolean
  onChangeActiveProject?: (project: string) => void
  onCreateProject?: VoidFunction
}

const default_avatar = '/images/default_avatar.svg'
const gift = '/images/gift.png'

export const PageHeader: React.FC<PageHeaderProps> = ({ tabName, classes, ...props }) => {
  const { t } = useTranslation([Namespace.GLOBAL])
  const router = useRouter()
  const user = useUserStore((state) => state.user)
  const updateUser = useUserStore((state) => state.updateUser)

  const {
    showLogo = true,
    // projects = ['Default project'],
    // activeProject = 'Default project',
    // onCreateProject,
    onChangeActiveProject,
  } = props

  const dropdownRef = useRef<HTMLDivElement>(null)
  const triggerRef = useRef<HTMLDivElement>(null)
  const [selectVisble, setSelectVisble] = useState(false)
  const [openContactModal, setOpenContactModal] = useState(false)

  const { theme, toggleTheme } = useTheme()
  const [dark, setDark] = useState(theme === 'dark')
  const [light, setLight] = useState(true)
  const [headerWidth, setHeaderWidth] = useState('100%')
  const { language, changeLanguage } = useLanguage({ loggedIn: true })

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        triggerRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        setSelectVisble(false)
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])
  useEffect(() => {
    if (user) {
      setLight(!user?.setting?.disableToolbar)
    }
  }, [user])
  // const handleCreateProject = useCallback(() => {
  //   onCreateProject?.()
  //   setSelectVisble(false)
  // }, [onCreateProject])
  // 更新划线工具条
  const changeHighlight = async (status: boolean) => {
    const res = await updateSetting({
      disableToolbar: !status,
    })
    if (user)
      updateUser({
        ...user,
        setting: {
          ...user.setting,
          disableToolbar: res.disableToolbar,
        },
      })
  }
  const handleLogout = () => {
    signOut({ callbackUrl: smartiesRoutes.logout })
  }

  const handleOpenContactUs = () => {
    setOpenContactModal(true)
  }
  const handleWindowWidth = () => {
    const scrollWidth = document.body.scrollWidth
    if (scrollWidth > 1116) {
      setHeaderWidth('100%')
    } else {
      setHeaderWidth('1116px')
    }
  }
  useEffect(() => {
    window.addEventListener('resize', handleWindowWidth)
    return () => {
      window.removeEventListener('resize', handleWindowWidth)
    }
  }, [])
  return (
    <>
      <div
        style={{ width: headerWidth }}
        className={clsx(
          'z-10 flex h-16 w-full items-center justify-between border-b bg-card',
          classes,
        )}>
        {showLogo ? (
          <div className='flex-center h-full w-64 border-r border-border'>
            <TopBar className='border-b-0' />
          </div>
        ) : null}
        <div className='ml-4 flex flex-auto shrink-0 items-center gap-1 bg-card'>
          <TabsComponent tabName={tabName} />
        </div>
        <Image
          src={gift}
          alt='gift'
          width={32}
          height={32}
          className='mr-3 cursor-pointer'
          onClick={() => {
            if (router.pathname !== smartiesRoutes.subscription) {
              router.push({ pathname: smartiesRoutes.subscription, query: { tab: 'referral' } })
            }
          }}
        />
        <Credits />
        <div ref={triggerRef}>
          <DropdownMenu open={selectVisble} modal={false}>
            <DropdownMenuTrigger asChild>
              <div
                className='flex-v-center ml-4 mr-3 h-10 flex-none shrink-0 cursor-pointer'
                onClick={() => setSelectVisble((pre) => !pre)}>
                {user && (
                  <>
                    <div className='flex-v-center h-10 w-10 shrink-0 justify-center rounded-md bg-secondary'>
                      {user?.picture ? (
                        <Image
                          src={user.picture}
                          alt='avatar'
                          width={36}
                          height={36}
                          className='h-9 w-9 rounded-md'
                        />
                      ) : (
                        <Image
                          src={default_avatar}
                          alt='avatar'
                          width={36}
                          height={36}
                          className='h-9 w-9 rounded-md'
                        />
                      )}
                    </div>
                    <div className='ml-1 shrink-0'>
                      <div className='flex gap-1'>
                        <Text type={TextEnum.Body_medium} className='text-foreground'>
                          {user?.name}
                        </Text>
                        {user && user?.currentPlan !== SubscriptionEnum.FREE && (
                          <div
                            className={clsx(
                              'flex-center ai-smarties-color-bg rounded px-1.5 italic text-primary-foreground',
                            )}>
                            <Text type={TextEnum.H7}>{t('setting.usage.pro')}</Text>
                          </div>
                        )}
                      </div>

                      <Text type={TextEnum.Body_small} className='mt-1 text-secondary-black-3'>
                        {t('setting.project.default')}
                      </Text>
                    </div>
                  </>
                )}
                <div className='ml-2 mr-2 h-5 border-l-0 border-r-[1px] border-solid border-border' />
                <ChevronDownIcon className='h-4 w-4 cursor-pointer text-secondary-black-1' />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent className='mr-1 w-56' sideOffset={16} ref={dropdownRef}>
              <DropdownMenuLabel>
                <Text type={TextEnum.Body_medium} className='text-foreground'>
                  {user?.name}
                </Text>
                <Text type={TextEnum.Body_small} className='mt-1 text-secondary-black-3'>
                  {user?.email}
                </Text>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem disabled className='pl-1'>
                <Text type={TextEnum.Body_small}>{t('setting.project.title')}</Text>
              </DropdownMenuItem>
              {[t('setting.project.default')].map((item, idx) => {
                return (
                  <DropdownMenuItem
                    key={item}
                    onClick={() => {
                      onChangeActiveProject?.(item)
                      setSelectVisble(false)
                    }}>
                    <Avatar className='mr-2 h-4 w-4 bg-primary'>
                      <AvatarFallback className={idx ? 'bg-dangerous-main' : 'bg-primary'}>
                        <Text type={TextEnum.Body_small} className='text-primary-foreground'>
                          {item[0].toUpperCase()}
                        </Text>
                      </AvatarFallback>
                    </Avatar>
                    <Text type={TextEnum.Body_medium}>{item}</Text>
                    {t('setting.project.default') === item ? (
                      <DropdownMenuShortcut>
                        <CheckIcon className='h-4 w-4' />
                      </DropdownMenuShortcut>
                    ) : null}
                  </DropdownMenuItem>
                )
              })}
              <DropdownMenuLabel></DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem disabled className='cursor-pointer pl-1'>
                <Text type={TextEnum.Body_small}>{t('setting.title')}</Text>
              </DropdownMenuItem>
              <DropdownMenuItem
                className='cursor-pointer'
                onClick={() => {
                  if (router.pathname !== smartiesRoutes.subscription) {
                    router.push(smartiesRoutes.subscription)
                  }
                  setSelectVisble(false)
                }}>
                <UserCircleIcon className='mr-2 h-4 w-4' />
                <Text type={TextEnum.Body_medium}>{t('setting.subscription')}</Text>
              </DropdownMenuItem>
              <DropdownMenuItem className='cursor-pointer'>
                <SunIcon className='mr-2 h-4 w-4' />
                <Text type={TextEnum.Body_medium}>{t('setting.mode')}</Text>
                <DropdownMenuShortcut>
                  <Switch
                    checked={dark}
                    onCheckedChange={(val) => {
                      toggleTheme()
                      setDark(val)
                    }}
                  />
                </DropdownMenuShortcut>
              </DropdownMenuItem>
              <DropdownMenuItem className='cursor-pointer'>
                <UnderlineIcon className='mr-2 h-4 w-4' />
                <Text type={TextEnum.Body_medium}>{t('setting.highlight')}</Text>
                <DropdownMenuShortcut>
                  <Switch
                    checked={light}
                    onCheckedChange={(val) => {
                      changeHighlight(val)
                      setLight(val)
                    }}
                  />
                </DropdownMenuShortcut>
              </DropdownMenuItem>
              <DropdownMenuGroup>
                <DropdownMenuSub>
                  <DropdownMenuSubTrigger>
                    <LanguageIcon className='mr-2 h-4 w-4' />
                    <Text type={TextEnum.Body_medium}>{t('setting.lang')}</Text>
                  </DropdownMenuSubTrigger>
                  {/* <DropdownMenuPortal> */}
                  <DropdownMenuSubContent>
                    {LANGUAGEOPTIONS.map((option) => {
                      return (
                        <DropdownMenuItem
                          className='cursor-pointer'
                          onClick={() => {
                            if (language !== option.value) changeLanguage(option.value)
                            setSelectVisble(false)
                          }}
                          key={option.value}>
                          <Text type={TextEnum.Body_medium} className='flex w-full items-center'>
                            <span className='mr-1'>{option.label}</span>
                            {option.beta && (
                              <Image src='/images/bate.svg' alt='bate' width={40} height={14} />
                            )}
                          </Text>
                          {language === option.value ? (
                            <DropdownMenuShortcut>
                              <CheckIcon className='h-4 w-4' />
                            </DropdownMenuShortcut>
                          ) : null}
                        </DropdownMenuItem>
                      )
                    })}
                  </DropdownMenuSubContent>
                  {/* </DropdownMenuPortal> */}
                </DropdownMenuSub>
              </DropdownMenuGroup>
              <DropdownMenuItem
                className='cursor-pointer'
                onClick={() => {
                  setSelectVisble(false)
                  handleOpenContactUs()
                }}>
                <UserGroupIcon className='mr-2 h-4 w-4' />
                <Text type={TextEnum.Body_medium}>{t('setting.contact')}</Text>
              </DropdownMenuItem>
              <DropdownMenuItem
                className='cursor-pointer'
                onClick={() => {
                  window.open('https://discord.com/invite/4tnKaZGkpx', '_blank')
                }}>
                <ChatBubbleLeftRightIcon className='mr-2 h-4 w-4' />
                <Text type={TextEnum.Body_medium}>{t('setting.discord')}</Text>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className='cursor-pointer' onClick={handleLogout}>
                <ArrowRightStartOnRectangleIcon className='mr-2 h-4 w-4' />
                <Text type={TextEnum.Body_medium}>{t('setting.logout')}</Text>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <ContactUsModal
        open={openContactModal}
        onClose={() => {
          setOpenContactModal(false)
        }}
      />
    </>
  )
}
