import { useState } from 'react'
import { DocTitle } from './DocTitle'
import { ModalSetting } from '../modal/ModalSetting'
import { DocEmpty } from './DocEmpty'
import { useReport } from '../../store/useReport'
import { useGenerate } from '../../store/useGenerate'
import { DocGenerator } from './DocGenerator'
import { DocumentLoading } from './DocSkeleton'
import clsx from 'clsx'

export const DocumentIndex = () => {
  const currentReport = useReport((s) => s.currentReport)
  const isLoadingDocument = useReport((s) => s.isLoadingDocument)
  const isSetDescription = useGenerate((s) => s.isSetDescription)
  const [openSetModal, setModal] = useState(false)

  return (
    <div className={clsx('hide-scrollbar flex-h-center flex-1 overflow-auto')}>
      <div className={clsx('mx-12 mt-12 min-w-[600px] max-w-[692px]')}>
        {isLoadingDocument ? (
          <DocumentLoading />
        ) : (
          <>
            {isSetDescription ? (
              <DocumentLoading />
            ) : (
              <>
                <DocTitle />
                {!currentReport.type ? (
                  <DocEmpty onSetModal={() => setModal(true)} />
                ) : (
                  <DocGenerator />
                )}
              </>
            )}
          </>
        )}
      </div>
      <ModalSetting open={openSetModal} handleCancel={() => setModal(false)}></ModalSetting>
    </div>
  )
}
