import { Text, TextEnum } from '@/components/business/text'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Namespace } from '@/i18n'
import { cn } from '@/lib/utils'
import { SearchModeEnum } from '@/types'
import { PaperAirplaneIcon } from '@heroicons/react/24/outline'
import { StopCircleIcon } from '@heroicons/react/24/solid'
import clsx from 'clsx'
import AutoIcon from 'public/images/auto.svg'
import ReasoningIcon from 'public/images/reasoning.svg'
import DeepSearchIcon from 'public/images/deepsearch.svg'
import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Button } from '@/components/ui/button'
import { Loader } from 'lucide-react'

export interface TextinputProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  disabled?: boolean
  showStop?: boolean
  isStopEnterPrevent?: boolean
  maxHeight?: number
  searchMode: SearchModeEnum
  onStop?: () => void
  onChangeSearchMode: (mode: SearchModeEnum) => void
  onSearch?: (key: string, mode: SearchModeEnum, isResearching: boolean) => void
  onEnter?: (value: string, mode: SearchModeEnum, isResearching: boolean) => void
  isShowDeepSearch?: boolean
}

const SearchInput = React.forwardRef<HTMLTextAreaElement, TextinputProps>(
  (
    {
      className,
      onEnter,
      disabled,
      showStop,
      onStop,
      onSearch,
      value,
      onChange,
      maxHeight = 144,
      searchMode = SearchModeEnum.AUTO,
      onChangeSearchMode,
      isStopEnterPrevent = true,
      isShowDeepSearch = false,
      ...props
    },
    ref,
  ) => {
    const { t } = useTranslation([Namespace.BASIC_SEARCH, Namespace.DEEPRESEARCH])
    const textareaRef = React.useRef<HTMLTextAreaElement | null>(null)
    const [isComposing, setIsComposing] = useState(false)
    const [isFocused, setIsFocused] = useState(false)
    const [isResearching, setIsResearching] = useState(false)

    React.useEffect(() => {
      adjustHeight()
    }, [value])

    // 动态调整 textarea 高度
    const adjustHeight = () => {
      const textarea = textareaRef.current
      if (textarea) {
        textarea.style.height = 'auto' // 先重置高度
        // const maxHeight = maxHeight // 设置最大高度
        const newHeight = textarea.scrollHeight // 获取内容的实际高度

        if (newHeight > maxHeight) {
          textarea.style.height = `${maxHeight}px` // 超过最大高度，设置为 330px
          textarea.style.overflowY = 'auto' // 显示滚动条
          textarea.style.resize = 'none' // 禁止 resize
        } else {
          textarea.style.height = `${newHeight}px` // 根据内容动态设置高度
          textarea.style.overflowY = 'hidden' // 隐藏滚动条
          textarea.style.resize = 'none' // 禁止 resize
        }
      }
    }

    // 处理按键事件
    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (isStopEnterPrevent) {
        if (e.key === 'Enter') {
          if (e.shiftKey) {
            adjustHeight()
          } else if (!isComposing) {
            e.preventDefault() // 阻止默认换行行为
            if (onEnter && !showStop && !disabled) {
              onEnter(textareaRef.current?.value || '', searchMode, isResearching)
            }
          }
        } else {
          adjustHeight()
        }
      } else {
        adjustHeight()
      }
    }

    // 处理输入法状态
    const handleCompositionStart = () => {
      setIsComposing(true)
    }

    // compositionend 表示中文字符输入结束
    const handleCompositionEnd = () => {
      setIsComposing(false)
      adjustHeight()
    }

    // 处理输入事件（包括删除）
    const handleInput = () => {
      adjustHeight()
    }

    const handleIconClick = () => {
      if (showStop && onStop) {
        onStop()
      }
      if (!showStop && !disabled && onSearch) {
        onSearch(textareaRef?.current?.value ?? '', searchMode, isResearching)
      }
    }

    React.useEffect(() => {
      adjustHeight()
    }, [])

    const searchModeOptions = [
      {
        value: SearchModeEnum.AUTO,
        tag: (
          <div className='flex items-center'>
            <AutoIcon />
            <div className='ml-2'>
              <Text type={TextEnum.Body_medium}>{t('homePage.auto')}</Text>
            </div>
          </div>
        ),
        icon: <AutoIcon />,
      },
      {
        value: SearchModeEnum.REASONING,
        tag: (
          <div className='flex items-center'>
            <ReasoningIcon />
            <div className='ml-2'>
              <Text type={TextEnum.Body_medium}>{t('homePage.reasoning')}</Text>
            </div>
          </div>
        ),
        icon: <ReasoningIcon />,
      },
    ]

    return (
      <div
        className={clsx(
          'relative rounded-lg border bg-card p-4',
          isFocused ? 'border-blue-500 ring-2 ring-ring ring-offset-2' : '',
          className,
        )}>
        <textarea
          className={cn(
            'order-2 flex min-h-[40px] w-full rounded-md border-none bg-card p-0 text-base outline-none ring-0 ring-offset-0 placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50',
            'focus:border-none focus:shadow-none focus:outline-none focus:ring-0 focus:ring-transparent focus-visible:outline-none',
            className,
          )}
          ref={(el) => {
            textareaRef.current = el
            if (typeof ref === 'function') {
              ref(el)
            } else if (ref) {
              ;(ref as React.MutableRefObject<HTMLTextAreaElement | null>).current = el
            }
          }}
          rows={1} // 默认显示一行
          onKeyDown={handleKeyDown}
          onInput={handleInput} // 处理删除和输入
          onCompositionStart={handleCompositionStart} // 中文输入开始
          onCompositionEnd={handleCompositionEnd} // 中文输入结束
          value={value}
          onChange={onChange}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...props}
        />
        <div className={clsx('order-1 mt-2 flex items-center justify-between')}>
          <div className='flex items-center gap-2'>
            <Select
              defaultValue={searchMode}
              onValueChange={(value: SearchModeEnum) => {
                onChangeSearchMode(value)
              }}>
              <SelectTrigger
                className={clsx(
                  'flex w-auto cursor-pointer items-center justify-center rounded-sm border hover:border-[#DFE8FF]',
                  'border-primary-disabled py-1.5 text-primary hover:border-primary-disabled hover:bg-[#EDF3FF] hover:text-[#7C8EFD]',
                )}>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {searchModeOptions.map((option) => (
                  <SelectItem
                    key={option.value}
                    value={option.value}
                    className='text-secondary-black-2'>
                    {option.tag}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {isShowDeepSearch && (
              <Button
                onClick={() => {
                  setIsResearching(!isResearching)
                }}
                variant='noHover'
                size='mini'
                className={clsx(isResearching ? 'bg-background text-primary' : '')}>
                <DeepSearchIcon className='mr-1 h-4 w-4'></DeepSearchIcon>
                <Text type={TextEnum.Body_medium}>{t('deepResearch:common.search')}</Text>
              </Button>
            )}
          </div>
          <div
            className={clsx(
              'flex h-[30px] transform items-center justify-center rounded-sm bg-primary px-[7px] text-white',
              disabled ? 'bg-primary-disabled' : 'cursor-pointer',
            )}
            onClick={handleIconClick}>
            {showStop ? (
              <>
                <StopCircleIcon className='mr-1 h-4 w-4' />
                <Text type={TextEnum.Body_medium}>{t('common.stop')}</Text>
              </>
            ) : (
              <>
                {disabled && <Loader className='mr-1 h-4 w-4 animate-spin360' />}
                <PaperAirplaneIcon className='h-4 w-4' />
              </>
            )}
          </div>
        </div>
      </div>
    )
  },
)
SearchInput.displayName = 'Searchinput'

export { SearchInput }
