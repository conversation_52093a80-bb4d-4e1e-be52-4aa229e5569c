import { GetServerSidePropsContext, NextPageContext } from 'next/types'
import nookies from 'nookies'

export const findCommaSeparatedValue = (rawString: string, name: string): string | undefined => {
  const regex = new RegExp(`(?:^|;)\\s*${name}\\s*=\\s*([^;]+)`)
  const matches = regex.exec(rawString)
  return matches ? matches[1] : undefined
}

export const smartiesDomain =
  process.env.NODE_ENV === 'development' ? 'localhost' : 'ai-smarties.com'

export const getCookie = (
  name: string,
  ctx?: NextPageContext | GetServerSidePropsContext | null,
): string | undefined => {
  const cookies = nookies.get(ctx)
  const value = cookies[name]
  return value || undefined
}

// define it to avoid adding cookie package
export interface CookieSerializeOptions {
  domain?: string | undefined
  encode?(value: string): string
  expires?: Date | undefined
  httpOnly?: boolean | undefined
  maxAge?: number | undefined // milliseconds
  path?: string | undefined
  priority?: 'low' | 'medium' | 'high' | undefined
  sameSite?: true | false | 'lax' | 'strict' | 'none' | undefined
  secure?: boolean | undefined
}

export const setCookie = (
  cookieName: string,
  cookieValue: string,
  options?: CookieSerializeOptions,
  ctx?: NextPageContext | GetServerSidePropsContext,
) => {
  const cookieOptions: CookieSerializeOptions = {
    path: '/',
    sameSite: 'none',
    secure: true,
    httpOnly: true,
    ...options,
  }
  nookies.set(ctx, cookieName, cookieValue, cookieOptions)
}

export const setLangToCookies = (
  lang: string,
  ctx?: NextPageContext | GetServerSidePropsContext,
) => {
  if (ctx)
    setCookie('lang', lang, {
      maxAge: 7 * 24 * 60 * 60 * 1000,
      httpOnly: false,
    })
  else
    setCookie(
      'lang',
      lang,
      {
        maxAge: 7 * 24 * 60 * 60 * 1000,
        httpOnly: false,
      },
      ctx,
    )
}
