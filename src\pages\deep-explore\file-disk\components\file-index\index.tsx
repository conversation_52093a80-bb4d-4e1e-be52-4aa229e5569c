import { TextEnum, Text } from '@/components/business/text'
import { Checkbox } from '@/components/ui/checkbox'
import { FileItem } from './FileItem'
import { useFile } from '../../store/useFile'
import { Namespace } from '@/i18n'
import { Trans, useTranslation } from 'react-i18next'
import { useEffect, useState, useRef } from 'react'
import { unitConversion } from '../../const'
import { FileSkeleton } from './FileSkeleton'
import { Progress } from '@/components/ui/progress'
import Image from 'next/image'

export const FileIndex = () => {
  const { t } = useTranslation([Namespace.DEEPEXPLORE, Namespace.GLOBAL, Namespace.BASIC_SEARCH])

  const selectedAll = useFile((s) => s.selectedAll)
  const fileData = useFile((s) => s.fileData)
  const selectedIds = useFile((s) => s.selectedIds)
  const search = useFile((s) => s.search)
  const setSearch = useFile((s) => s.setSearch)
  const toggleSelectItem = useFile((s) => s.toggleSelectItem)
  const toggleSelectAll = useFile((s) => s.toggleSelectAll)
  const setLoading = useFile((f) => f.setLoading)

  const getFileList = useFile((f) => f.getFileList)
  const totalSize = useFile((s) => s.totalSize)
  const maxSize = useFile((s) => s.maxSize)
  const isLoading = useFile((s) => s.isLoading)
  const totalPages = useFile((s) => s.totalPages)
  const [sizePercent, setSizePercent] = useState(0)
  const loadMoreRef = useRef<HTMLDivElement>(null)
  const rightResourceRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    setLoading(true)
    getFileList(true)
  }, [])

  const loadMore = async () => {
    if (!search.lastPageKey) return
    const currentPage = search.pageSize + 10
    try {
      setSearch({
        pageSize: currentPage,
        lastPageKey: null,
      })
    } catch (error) {
      console.error('loadMore error:', error)
    }
  }

  useEffect(() => {
    setSizePercent(Math.round((totalSize / maxSize) * 100))
  }, [totalSize, maxSize])

  const isLoadingsRef = useRef(false)

  useEffect(() => {
    const observer = new IntersectionObserver(
      async (entries) => {
        if (isLoadingsRef.current) return
        if (entries[0].isIntersecting) {
          isLoadingsRef.current = true
          await loadMore()
          isLoadingsRef.current = false
        }
      },
      {
        root: rightResourceRef.current,
        threshold: 1,
      },
    )

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current)
    }

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current)
      }
    }
  }, [rightResourceRef.current, loadMoreRef.current, search.lastPageKey])

  return (
    <div className='mt-5'>
      {isLoading ? (
        <FileSkeleton />
      ) : (
        <>
          <Text type={TextEnum.Body_medium} className='flex items-center px-[158px] pb-2'>
            <Checkbox
              className='mr-2 h-5 w-5 rounded-lg border-2 border-border'
              checked={selectedAll}
              onCheckedChange={toggleSelectAll}
            />
            {selectedIds.length || selectedAll ? (
              <Trans i18nKey='selectTip' t={t} components={{ num: selectedIds.length }} />
            ) : (
              <Trans i18nKey='totalFile' t={t} values={{ count: totalPages }} />
            )}
          </Text>
          {fileData.length ? (
            <div
              className='hide-scrollbar mt-4 h-[calc(100vh-190px)] overflow-auto'
              ref={rightResourceRef}>
              <div className='flex select-none flex-wrap gap-x-6 gap-y-8 px-[158px]'>
                {fileData.map((item, index) => (
                  <FileItem item={item} key={index} toggleSelectItem={toggleSelectItem} />
                ))}
              </div>
              {(search.lastPageKey || isLoadingsRef.current) && (
                <div ref={loadMoreRef} className='mt-3 flex h-10 items-center justify-center'>
                  <Text type={TextEnum.Body_small} className='mr-1'>
                    {t('global:button.loadMore')}
                  </Text>
                  <Image
                    src='/images/downloading.png'
                    alt=''
                    width={32}
                    height={32}
                    className='animate-spin360'
                  />
                </div>
              )}
            </div>
          ) : (
            <div className='flex-center mt-80 w-full flex-col'>
              <Image
                className='mr-3'
                src='/images/no_data.svg'
                alt='no data'
                width={48}
                height={48}
              />
              <Text type={TextEnum.Body_medium} className='text-gray'>
                {t('basicSearch:chat.rightSide.noData')}
              </Text>
            </div>
          )}

          <div className='fixed bottom-6 left-6 h-9 w-[113px] rounded-sm border border-[#F9D787] bg-[rgba(249,215,135,0.2)] px-2 pt-[5px]'>
            <Text type={TextEnum.Body_small}>
              {unitConversion(totalSize)}/{unitConversion(maxSize)}
            </Text>
            <div className='mt-0.5 h-1 w-full rounded-xs bg-[#DFE8FF]'>
              <Progress value={sizePercent} className='h-1'></Progress>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
