import { PDFItemType } from '@/api/pdfs/getPdf'
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable'
import React, { ReactNode, useImperativeHandle, useRef } from 'react'
import PdfPreview, {
  PDFPreviewComponentHandles,
} from '../../../../deep-explore/components/PdfPreview.client'
import { ChatPreview } from '../chat/ChatPreview'

interface PdfContainerProps {
  pdf?: PDFItemType
  isShowChat?: boolean
  children?: ReactNode
  header2Class?: string
  header3Class?: string
}

export const PdfPreviews = React.forwardRef(
  (
    {
      pdf,
      isShowChat = true,
      children,
      header2Class = 'h-screen-minus-header',
      header3Class = 'h-screen-minus-pdf-content',
    }: PdfContainerProps,
    ref,
  ) => {
    const childRef = useRef<PDFPreviewComponentHandles | null>(null)
    useImperativeHandle(ref, () => {
      return {
        updateInputPage: (page: number) => {
          childRef.current?.updateInputPage(page)
        },
      }
    }, [])
    return (
      <div>
        <ResizablePanelGroup direction='horizontal' className='overflow-hidden'>
          <ResizablePanel defaultSize={70} minSize={20}>
            <div className='h-full min-w-[420px]'>
              <PdfPreview
                selectedPdf={pdf}
                header2Class={header2Class}
                header3Class={header3Class}
                ref={childRef}
              />
            </div>
          </ResizablePanel>
          <ResizableHandle
            withHandle
            className='w-[2px] bg-border hover:bg-primary active:bg-primary'
          />
          <ResizablePanel defaultSize={30} minSize={30}>
            <div className='h-full min-w-[420px]'>
              {isShowChat && <ChatPreview chat={pdf} className='px-4'></ChatPreview>}
              {children}
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    )
  },
)
PdfPreviews.displayName = 'PdfPreviews'
