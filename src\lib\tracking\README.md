# 声明式埋点机制

本目录包含 AISmarties 的声明式埋点系统实现。该系统允许自动跟踪用户交互，无需在组件代码中手动添加埋点调用。

## 概述

埋点系统通过扫描 DOM 中带有特定埋点属性的元素，并自动附加事件监听器来跟踪用户交互。这种方法减少了手动埋点代码的需求，并确保了整个应用程序中埋点的一致性。

## 使用方法

### 基本用法

在任何你想要跟踪的 HTML 元素上添加 `asm-tracking` 属性：

```html
<button 
  asm-tracking="BUTTON_CLICK:CLICK" 
  asm-tracking-p-button_name="signup_button"
>
  注册
</button>
```

`asm-tracking` 属性的格式为：`"事件名称:事件类型"` 其中：
- `事件名称` 是你想要跟踪的事件的名称（例如，`BUTTON_CLICK`）
- `事件类型` 是要跟踪的交互类型（例如，`CLICK`）

### 添加自定义参数

你可以通过使用带有前缀 `asm-tracking-p-` 的属性向埋点事件添加自定义参数：

```html
<div 
  asm-tracking="SECTION_VIEW:VIEW" 
  asm-tracking-p-section_name="hero" 
  asm-tracking-p-section_position="top"
>
  <!-- 内容 -->
</div>
```

### 自定义埋点方法

你可以通过指定自定义方法来覆盖默认的埋点行为：

```html
<button 
  asm-tracking="CUSTOM_BUTTON:CLICK" 
  asm-tracking-method="myCustomTrackingMethod"
>
  自定义埋点
</button>
```

自定义方法应该是附加到 window 对象上的函数，该函数接受默认的埋点属性并返回修改后的属性：

```javascript
window.myCustomTrackingMethod = (defaultProps) => {
  return {
    ...defaultProps,
    custom_property: '自定义值',
    timestamp: new Date().toISOString()
  };
};
```

## 支持的事件类型

埋点系统支持以下事件类型：

- `VIEW`：当元素被渲染时触发
- `CLICK`：当元素被点击时触发
- `HOVER`：当鼠标进入元素时触发
- `FOCUS`：当元素获得焦点时触发
- `BLUR`：当元素失去焦点时触发
- `SUBMIT`：当表单被提交时触发
- `CHANGE`：当输入值发生变化时触发
- `KEYPRESS`：当按下键盘按键时触发

## 通用属性

以下通用属性会自动包含在每个埋点事件中：

- `url`：当前页面 URL
- `path`：当前页面路径
- `referrer`：引荐来源 URL
- `language`：用户的浏览器语言
- `screen_width`：用户的屏幕宽度
- `screen_height`：用户的屏幕高度
- `user_id`：用户 ID（如果可用）
- `timestamp`：事件触发时的时间

## 实现细节

埋点系统在 `tracking.ts` 中实现，并在应用程序的 `_app.tsx` 文件中初始化。系统使用 mutation observer 来检测具有埋点属性的新元素，并附加适当的事件监听器。

## 演示

在 `/tracking-demo` 页面上提供了一个演示，展示了各种埋点功能。

## 最佳实践

1. 使用描述性的事件名称，指明正在跟踪的内容
2. 在类似元素中保持参数名称的一致性
3. 使用通用前缀对相关的埋点事件进行分组
4. 记录自定义埋点方法
5. 在部署前在开发环境中测试埋点实现

## 故障排除

如果埋点事件未被发送：

1. 检查元素是否具有正确的 `asm-tracking` 属性
2. 验证 Mixpanel 是否正确初始化
3. 检查浏览器控制台是否有任何错误
4. 确保事件类型受支持
5. 验证自定义方法是否正确附加到 window 对象上

## 埋点高亮功能

埋点高亮功能允许开发人员可视化页面上的所有埋点元素。这对于调试和确保所有必要的元素都被正确埋点非常有用。

### 使用方法

1. 在 Mac 上按住 `Control + Option + T` 或在 Windows 上按住 `Control + Alt + T` 来激活高亮模式。
2. 所有带有 `asm-tracking` 属性的元素将会用彩色边框高亮显示。
3. 每个埋点元素的信息将在元素附近的工具提示中显示，包括：
   - 埋点事件名称
   - 触发方式（CLICK、VIEW 等）
   - 任何自定义参数（来自 `asm-tracking-p-*` 属性）
   - 自定义方法名称（如果指定）
4. 如果页面上没有找到埋点元素，右下角将显示一条消息，写着"no-tracking-point"。
5. 释放按键以退出高亮模式。

此功能在所有环境中自动启用，但仅在按下组合键时激活。

### 调试工具

还提供了一个调试工具，用于手动控制埋点高亮功能。这对于测试和调试埋点元素很有用，无需按住组合键。

你可以在浏览器控制台中访问调试工具：

```javascript
// 显示所有埋点元素
window.debugTracking.showTrackingElements()

// 隐藏所有埋点元素
window.debugTracking.hideTrackingElements()
```

你也可以在代码中使用导出的工具：

```javascript
import { debugTracking } from '@/utils/tracking'

// 显示所有埋点元素
debugTracking.showTrackingElements()

// 隐藏所有埋点元素
debugTracking.hideTrackingElements()
```
