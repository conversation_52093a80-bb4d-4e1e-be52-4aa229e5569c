import { cancelProcess } from '@/api/cancelProcess'
import { getInProgressTask, ProcessType } from '@/api/getInProgressTask'
import { getMedia, MediaItemType, MediaRetrieverType } from '@/api/getMedia'
import { getProcessDetail, GetProcessDetailResponse } from '@/api/getProcessDetail'
import {
  AssistantMessage,
  getSession,
  MessageRoleEnum,
  ReferenceItem,
  SingleMessageType,
  UserMessage,
} from '@/api/getSession'
import { getUser } from '@/api/getUser'
import { getPdfs, PDFItemType } from '@/api/pdfs/getPdf'
import { SourceTypeEnum } from '@/api/report/saveSource'
import { startQuest } from '@/api/startQuest'
import { Copy } from '@/components/business/copy'
import CreditsModal from '@/components/business/credits-modal/CreditsModal'
import { MenuComponentHandles } from '@/components/business/layout/Menu'
import LoadingDots from '@/components/business/loading-dots'
import MarkdownParser from '@/components/business/markdown-parser'
import { Recommend } from '@/components/business/recommend'
import { SelectDropdown } from '@/components/business/select-dropdown'
import { Text, TextEnum } from '@/components/business/text'
import { Namespace } from '@/i18n'
import { Tracking } from '@/lib/tracking'
import { TrackingEventType } from '@/lib/tracking/types'
import useWebSocketWithReconnection from '@/pages/deep-explore/socket'
import { smartiesRoutes } from '@/routers'
import { useUserStore } from '@/store/userStore'
import {
  ImageStatusEnum,
  MessageType,
  PdfStatusEnum,
  PoTypeEnum,
  ProcessStatusEnum,
  SearchModeEnum,
  ShareTypeEnum,
  SubscriptionEnum,
} from '@/types'
import {
  ArrowPathIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  ChevronUpIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline'
import clsx from 'clsx'
import { debounce, throttle } from 'lodash'
import Image from 'next/image'
import { useRouter } from 'next/router'
import DeepSearchIcon from 'public/images/deepsearch.svg'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'sonner'
import BasicSearchLayout from '../BasicSearchLayout'
import ChatFooter from './Footer'
import { InProgressProcess } from './InProgressProcess'
import { messagesMap } from './messageMapping'
import PageSkeleton from './PageSkeleton'
import { QuestionEditor } from './QuestionEditor'
import ReferenceList from './ReferenceList'
import StopModal from './StopModal'
import ShareLink from '@/components/business/share-link'
import { GeneralSocketMessage, SocketMessageData } from '@/pages/deep-explore/utils'

const isProcessFinished = (status: ProcessStatusEnum) => {
  return (
    status === ProcessStatusEnum.FINISH ||
    status === ProcessStatusEnum.FAILED ||
    status === ProcessStatusEnum.CANCELED
  )
}
/**
 * 1. 进入页面，查询是否有进行中的对话
 * 2. 有：说明上次对话未推流完成，此时需要获取上一次推流内容（获取详情，判断是否展示右侧资源）
 * 3. 没有：说明上次对话推流完成
 * 4. 查询对话记录，获取详情，判断是否展示右侧资源
 */
/**
 * 对话状态，已有的状态
 * STARTING--开始
 * FINISH_USERINTENT --完成用户意图
 * FINISH_SEARCH --搜索结束
 * imageStatus -- IN_PROGRESS - 进行中  FINISH - 完成
 * FINISH_WEBCRAWLER --提取结束
 * FINISH -- 对话结束
 * FAILED -- 对话失败
 * CANCELED -- 取消对话
 */
const SearchChatPage = () => {
  const { t } = useTranslation([Namespace.BASIC_SEARCH, Namespace.GLOBAL])
  const router = useRouter()

  const user = useUserStore((state) => state.user)
  const updateUser = useUserStore((state) => state.updateUser)

  const { type, sessionId } = router.query // normal

  const [loading, setLoading] = useState(true)

  const [openStopModal, setOpenStopModal] = useState(false)

  // 当前是否是追问状态
  const [isFirstQuery, setIsFirstQuery] = useState(true) // top input status

  useEffect(() => {
    setChatTitle(t(`homePage.card.title_${(type as string).toLocaleLowerCase()}`))
  }, [t])

  // 右侧资源状态
  const [pdfSearching, setPdfSearching] = useState<boolean>(false)
  const [imgsSearching, setImgsSearching] = useState<boolean>(false)
  const [pdfs, setPdfs] = useState<Array<PDFItemType> | undefined>(undefined)
  const [imgs, setImgs] = useState<Array<MediaItemType> | undefined>(undefined)
  const [pdfProcessId, setPdfProcessId] = useState<string>('')

  // chat
  const [chatTitle, setChatTitle] = useState('')
  const [messages, setMessages] = useState<SingleMessageType[]>([])
  const [pollingProcess, setPollingProcess] = useState<ProcessType | null>(null)

  // 原始第一个 query message
  const [firstMessage, setFirstMessage] = useState<SingleMessageType | null>(null)

  // controll status 底部输入框
  const [showStop, setShowStop] = useState(false) // bottom input status
  const [disableBottomInput, setDisableBottomInput] = useState(false)

  // controll status 顶部输入框
  const [disabledEdit, setDisabledEdit] = useState(false) // top input status
  // 第一个 query message是否被编辑过
  const [isEdited, setIsEdited] = useState<boolean>(false)
  // 最后一个 user message ref
  const lastUserQuestionRef = useRef<HTMLDivElement>(null)

  // 更新左侧菜单 ref
  const menuRef = useRef<MenuComponentHandles | null>(null)

  const [openCreditsModal, setOpenCreditsModal] = useState(false)

  // 当前是否有 failed
  const [queryFailed, setQueryFailed] = useState(false)

  const [showProgress, setShowProgress] = useState(false)
  // const [permission, setPermission] = useState<NotificationPermission>()

  const [expendReasoning, setExpendReasoning] = useState<
    Array<{ processId?: string; expand: boolean }>
  >([])

  const [showShareButton, setShowShareButton] = useState<boolean>(false)

  const containerRef = useRef<HTMLDivElement>(null)
  const manualScrolledRef = useRef(false)

  const queryImages = async ({ processId }: { processId: string }) => {
    try {
      setImgsSearching(true)
      const imgDetail = await getMedia({
        processId,
        type: MediaRetrieverType.IMAGE,
        ifAll: true,
      })
      setImgs(imgDetail)
      setImgsSearching(false)
    } catch (error) {
      console.error('get session inof error: ', error)
      setImgsSearching(false)
    }
  }

  const queryPdfs = async ({ processId }: { processId: string }) => {
    try {
      setPdfSearching(true)
      pdfList = await getPDFList(processId)
      setPdfs(pdfList)
      setPdfSearching(false)
    } catch (error) {
      console.error('get pdf list error: ', error)
      setPdfSearching(false)
    }
  }

  // get 聊天记录
  const queryChatList = async (sesId?: string): Promise<SingleMessageType[]> => {
    try {
      const chatList = await getSession({ sessionId: sesId || (sessionId as string) })
      if (chatList === null) {
        router.push({
          pathname: smartiesRoutes.basicSearch.home,
        })
      }
      if (chatList.messages) {
        return chatList.messages
      }
    } catch (error) {
      return []
    }
    return []
  }

  const getPDFList = async (processId: string): Promise<PDFItemType[]> => {
    try {
      setPdfProcessId(processId)
      const res = await getPdfs({
        processId,
      })
      if (res && res.pdfList.length > 0) {
        return res.pdfList as PDFItemType[]
      }
      return []
    } catch (error) {
      setPdfProcessId('')
      return []
    }
  }

  // 查询未完成的 task & 轮询
  const queryInProcessTask = async ({ processId }: { processId: string }) => {
    const processDetail = await getProcessDetail({
      processId,
    })
    if (processDetail.process?.needSearch === true) {
      // 更新 PDF & image
      if (
        processDetail.process?.downloadPdf === PdfStatusEnum.FINISH ||
        processDetail.process?.status === ProcessStatusEnum.FINISH ||
        processDetail.process?.status === ProcessStatusEnum.FAILED
      ) {
        await queryPdfs({ processId: processDetail?.process.processId })
      }
      if (processDetail.process?.saveImage === ImageStatusEnum.FINISH) {
        await queryImages({
          processId: processDetail?.process.processId,
        })
      } else {
        //异常处理
        setImgsSearching(false)
      }
    }
    return processDetail
  }
  const currentProcessId = useRef<string | undefined | null>()
  const cachedProcessMessage = useRef<Record<string, SingleMessageType[]>>()
  // init
  const initEnterPage = async (sessionId: string) => {
    setPdfs([])
    setImgs([])
    setPollingProcess(null)
    setQueryFailed(false)
    setLoading(true)
    setIsFirstQuery(true)
    setPdfProcessId('')
    if (sessionId) {
      await updateCredits()
      const messages = await queryChatList(sessionId)
      isQueryFirst.current = 'first'

      /**
       * 1. 查询未完成的任务
       * 2. 存在未完成的继续输出，也需要查询右侧资源
       * 3. 任务都完成获取右侧资源
       */
      const setSteamMessage = (processDetail: GetProcessDetailResponse) => {
        messagesRef.current = [
          ...messages,
          {
            role: MessageRoleEnum.ASSISTANT,
            processId: processDetail.process?.processId || '',
            content: '',
            reasoning: '',
            referenceList: processDetail.referenceList,
          },
        ]
      }

      const processList = await getInProgressTask({ sessionId: sessionId as string })
      if (processList && processList.process) {
        setShowStop(true)
        if (
          cachedProcessMessage.current &&
          cachedProcessMessage.current[processList.process?.processId]
        ) {
          setMessages(
            messagesMap(
              type as PoTypeEnum,
              cachedProcessMessage.current[processList.process?.processId],
            ),
          )
          setPollingProcess(null)
          setLoading(false)
          setIsShowReference(false)
        } else {
          setMessages(messagesMap(type as PoTypeEnum, messages ?? []))
          setLoading(false)
          scrollChatList()
          setIsShowReference(true)
          setPollingProcess(processList.process)
        }
        currentProcessId.current = processList.process?.processId
        scrollChatList()
        const processDetail = await queryInProcessTask({
          processId: processList.process?.processId ?? '',
        })
        setSteamMessage(processDetail)
      } else {
        setShowStop(false)
        pdfList = []
        setIsShowReference(true)
        setMessages(messagesMap(type as PoTypeEnum, messages ?? []))
        setLoading(false)
        scrollChatList()
        currentProcessId.current = messages[0].processId
        const processDetail = await queryInProcessTask({
          processId: messages[0].processId ?? '',
        })
        if (processDetail.process?.status === ProcessStatusEnum.FINISH) {
          setShowShareButton(true)
        }
        setSteamMessage(processDetail)
      }
    } else {
      setLoading(false)
    }
  }
  const debouncedInitEnterPage = useCallback(
    debounce((sessionId) => {
      console.log('真正执行 initEnterPage:', sessionId)
      initEnterPage(sessionId)
      initWebSocket()
    }, 500),
    [],
  )

  useEffect(() => {
    debouncedInitEnterPage(sessionId)
  }, [sessionId])

  const updateCredits = async () => {
    const user = await getUser()
    if (user) updateUser(user)
    return user
  }

  useEffect(() => {
    ;(async () => {
      await updateCredits()
    })()
  }, [])

  // 保存未被编辑过的 query message - 在本页编辑后退出，还原 message
  useEffect(() => {
    if (!isFirstQuery) return
    if (messages.length > 0 && !isEdited) {
      setFirstMessage(messages[0])
    }
  }, [messages])

  const handleCancelEdit = () => {
    setMessages((prevArray) => [firstMessage as SingleMessageType, ...prevArray.slice(1)])
  }

  // 编辑问题
  const handleUpdateQuestion = (type: PoTypeEnum, newMessages: MessageType) => {
    setIsEdited(true)
    setMessages((prevArray) => [
      {
        content: newMessages,
        role: MessageRoleEnum.USER,
      },
      ...prevArray.slice(1),
    ])
  }
  const isChatIng = useRef(false)
  // 打开新会话
  const handleSubmitEdit = async () => {
    const res = await checkCredits()
    if (!res) return
    try {
      setIsFirstQuery(true)
      const resp = await startQuest({
        message: messages[0].content as unknown as MessageType,
        poType: (type as string).toUpperCase() as PoTypeEnum,
      })
      // Tracking.trackPageView({
      //   eventName: 'CLICK_START_CHAT_BUTTON',
      //   properties: {
      //     sessionId: resp.sessionId,
      //   },
      // })
      Tracking.trackEvent('CLICK_START_CHAT_BUTTON', TrackingEventType.CLICK, {
        sessionId: resp.sessionId,
      })
      isChatIng.current = true
      if (resp.sessionId && resp.processId)
        router.push({
          pathname: smartiesRoutes.basicSearch.chat(type as string),
          query: {
            processId: resp.processId,
            sessionId: resp.sessionId,
          },
        })
      // 更新二级菜单
      if (menuRef.current) {
        menuRef.current?.updateSecondMenu((type as string).toUpperCase() as PoTypeEnum)
      }
      // toast 请求异常，请稍后
    } catch (error) {
      // toast 请求异常，请稍后
      console.error('query', error)
    }
  }
  const messagesRef = useRef<Array<SingleMessageType>>([])
  const isQueryFirst = useRef('')
  // 追问
  const handleSendQuery = async (query: string, mode: SearchModeEnum) => {
    // 检查 socket 连接
    checkSocket()
    const res = await checkCredits()
    if (!res) return
    if (query.trim().length > 0) {
      setQueryFailed(false)
      setIsFirstQuery(false)
      setDisableBottomInput(true)
      try {
        const resp = await startQuest({
          message: [
            {
              key: 'query',
              value: query,
            },
          ],
          sessionId: sessionId as string,
          useReasoning: mode === SearchModeEnum.REASONING ? true : undefined,
          poType: (type as string).toUpperCase() as PoTypeEnum,
        })

        await updateCredits()
        isChatIng.current = true
        setDisableBottomInput(false)
        setShowStop(true)
        if (resp.processId) {
          Tracking.trackEvent('CLICK_FURTHER_INQUIRY_BUTTON', TrackingEventType.CLICK, {
            processId: resp.processId,
          })
          isQueryFirst.current = 'furtherInquiry'
          currentProcessId.current = resp.processId
          const messages = await queryChatList()
          setMessages(messagesMap(type as PoTypeEnum, messages ?? []))
          messagesRef.current = [
            ...messages,
            {
              role: MessageRoleEnum.ASSISTANT,
              processId: resp.processId,
              content: '',
              reasoning: '',
              referenceList: [],
            },
          ]
          manualScrolledRef.current = false
          scrollChatList()
        }
      } catch (error) {
        setShowStop(false)
        setDisableBottomInput(false)
        setQueryFailed(true)
      }
    }
  }

  // 滚动屏幕
  const scrollChatList = () => {
    setTimeout(() => {
      if (lastUserQuestionRef.current && !manualScrolledRef.current) {
        lastUserQuestionRef.current.scrollIntoView({ behavior: 'smooth' })
      }
    }, 100)
  }

  // 取消查询
  const handleCancelQuery = async () => {
    if (currentProcessId.current) {
      await cancelProcess({
        processId: currentProcessId.current,
      })
      setPollingProcess(null)
      currentProcessId.current = null
      setShowStop(false)
      const messages = await queryChatList()
      setMessages(messagesMap(type as PoTypeEnum, messages ?? []))
      scrollChatList()
      isChatIng.current = false
      setDisabledEdit(false)
      if (messages && messages.length) {
        queryInProcessTask({
          processId: messages[0].processId ?? '',
        })
      }
      await updateCredits()
      if (isFirstQuery) {
        setPdfSearching(false)
        setImgsSearching(false)
      }
    }
    setOpenStopModal(false)
  }

  // 查询余额
  const checkCredits = async () => {
    // 校验次数
    if (
      user?.currentPlan === SubscriptionEnum.FREE ||
      user?.currentPlan === SubscriptionEnum.BASIC_MONTH ||
      user?.currentPlan === SubscriptionEnum.BASIC_YEAR
    ) {
      if (user.usage.totalQuota - user.usage.totalUsage <= 0) {
        setOpenCreditsModal(true)
        return false
      }
      return true
    } else if (
      // 不校验次数
      user?.currentPlan === SubscriptionEnum.PRO_MONTH ||
      user?.currentPlan === SubscriptionEnum.PRO_YEAR
    ) {
      return true
    } else {
      setOpenCreditsModal(true)
      return false
    }
  }

  const joinReference = (list?: ReferenceItem[]): string => {
    if ((list && list.length === 0) || !list) return ''
    const text = list
      .map((item, idx) => {
        return ` ${idx + 1} ${item.title} ${item.url}`
      })
      .join('\n')

    return `\n\n${t(`chat.refer`)}\n\n${text}`
  }
  const [isShowReference, setIsShowReference] = useState(false)
  // 追问的处理
  const setMessageMethod = (data: SocketMessageData, processId: string) => {
    if (data.summaryStream || data.reasoningStream) {
      setPollingProcess(null)
    }
    if (data.summaryStream || data.referenceList || data.reasoningStream) {
      const messageList = (
        messagesRef.current.map((m) => {
          if (m.processId === processId && m.role === MessageRoleEnum.ASSISTANT) {
            if (data.referenceList) {
              m.referenceList = data.referenceList
              setIsShowReference(false)
            }
            if (data.reasoningStream) {
              m.reasoning = data.reasoningStream
            }
            if (data.summaryStream) {
              m.content = data.summaryStream
            }
          }
          return m
        }) as AssistantMessage[]
      ).filter((s) => s.content || s.reasoning)
      if (cachedProcessMessage.current && !cachedProcessMessage.current[processId]) {
        cachedProcessMessage.current[processId] = messageList
      }
      setMessages(messagesMap(type as PoTypeEnum, messageList ?? []))
    }
  }
  let statusMessage = ProcessStatusEnum.STARTING

  const handleFurtherInquiry = (message: GeneralSocketMessage) => {
    const { processId, data } = message
    if (data?.status) {
      statusMessage = data.status
    }
    setPollingProcess({
      status: statusMessage,
      poType: (router.query.type as string).toUpperCase() as PoTypeEnum,
      processId,
    })
    scrollChatList()
    setMessageMethod(data, processId)
    if (data?.status === ProcessStatusEnum.FINISH) {
      isChatIng.current = false
      setShowStop(false)
      setPollingProcess(null)
      setShowShareButton(true)
      setDisabledEdit(false)
    }
    if (data?.status === ProcessStatusEnum.FAILED) {
      isChatIng.current = false
      setQueryFailed(true)
      setShowStop(false)
      setDisabledEdit(false)
      setPollingProcess(null)
    }
  }
  let pdfList = []
  const handleFirstQuery = (message: GeneralSocketMessage) => {
    const { data, processId } = message
    setShowStop(true)
    if (data.status) {
      statusMessage = data.status
    }
    setPollingProcess({
      status: statusMessage,
      poType: (type as string).toUpperCase() as PoTypeEnum,
      processId,
    })
    // 不需要展示右侧资源
    if (data?.needSearch === false) {
      setPdfs([])
      setImgs([])
      setImgsSearching(false)
      setPdfSearching(false)
      setMessageMethod(data, processId)
    } else {
      // 需要展示右侧资源
      if (data?.downloadPdf) {
        queryPdfs({ processId })
      }
      if (data?.imageStatus === ImageStatusEnum.IN_PROGRESS) {
        setImgsSearching(true)
      } else if (data?.imageStatus === ImageStatusEnum.FINISH) {
        setImgsSearching(false)
        queryImages({ processId })
      }
      setMessageMethod(data, processId)
    }

    if (data?.status === ProcessStatusEnum.FINISH) {
      isChatIng.current = false
      setShowStop(false)
      setIsShowReference(true)
      setShowShareButton(true)
      setDisabledEdit(false)
      if (pdfs?.length === 0) {
        queryPdfs({ processId })
      }
      if (imgs?.length === 0) {
        queryImages({ processId })
      }
    }
    if (data?.status === 'FAILED') {
      isChatIng.current = false
      setQueryFailed(true)
      setShowStop(false)
      setDisabledEdit(false)
      setPollingProcess(null)
      setImgsSearching(false)
      setPdfSearching(false)
    }
  }
  // websocket
  const handleSocketMessage = (message: GeneralSocketMessage) => {
    if (message.processId !== currentProcessId.current) {
      return
    }
    setDisabledEdit(true)
    if (isQueryFirst.current === 'furtherInquiry') {
      handleFurtherInquiry(message)
    } else if (isQueryFirst.current === 'first') {
      handleFirstQuery(message)
    }
  }
  useEffect(() => {
    const handleOffline = () => {
      onRefresh()
    }
    window.addEventListener('offline', handleOffline)
    return () => {
      window.removeEventListener('offline', handleOffline)
    }
  }, [])
  const onRefresh = () => {
    if (isChatIng.current) {
      toast(
        <div
          onClick={() => {
            window.location.reload()
          }}
          className='flex items-center'>
          <ArrowPathIcon className='mr-2 h-5 w-5 cursor-pointer'></ArrowPathIcon>
          {t('global:error.netError')}
        </div>,
        {
          duration: 3000,
        },
      )
    }
  }
  const { socket, isConnected, startWebSocket, retryFailed } = useWebSocketWithReconnection({
    onMessage: handleSocketMessage,
    onRetryRefresh: onRefresh,
  })

  const checkSocket = () => {
    if (isConnected) {
      return true
    } else {
      startWebSocket()
      return isConnected
    }
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const initWebSocket = () => {
    setDisabledEdit(false)
    isChatIng.current = false
    if (!socket || !isConnected) {
      startWebSocket()
      messagesRef.current = []
      currentProcessId.current = null
    }
  }

  // const requestPermission = () => {
  //   if (!('Notification' in window)) {
  //     alert('您的浏览器不支持通知')
  //     return
  //   }

  //   Notification.requestPermission().then((perm: NotificationPermission) => {
  //     setPermission(perm)
  //     if (perm === 'granted') {
  //       alert('通知权限已授予！')
  //     } else if (perm === 'denied') {
  //       alert('通知权限被拒绝')
  //     }
  //   })
  // }

  useEffect(() => {
    if (retryFailed) {
      setQueryFailed(true)
      onRefresh()
    }
  }, [retryFailed])

  const handleExpandReasoning = (processId: string) => {
    const nextExpendReasoning = expendReasoning.map((item) => {
      if (item.processId === processId) {
        return { ...item, expand: !item.expand }
      }
      return item
    })
    setExpendReasoning(nextExpendReasoning)
  }

  useEffect(() => {
    const expendReasoningObj = messages
      .filter((item) => item.role === MessageRoleEnum.ASSISTANT && item.reasoning)
      .map((expandItem) => {
        const initalExpend = expendReasoning.find(
          (item) => item.processId === expandItem.processId,
        )?.expand
        return {
          processId: expandItem.processId,
          expand: initalExpend === undefined ? true : initalExpend,
        }
      })
    setExpendReasoning(expendReasoningObj)
  }, [messages])

  useEffect(() => {
    const handleUserScroll = throttle(() => {
      manualScrolledRef.current = true
    }, 200)

    const container = containerRef.current
    if (container) {
      container.addEventListener('wheel', handleUserScroll)
      container.addEventListener('touchmove', handleUserScroll)
    }

    return () => {
      if (container) {
        container.removeEventListener('wheel', handleUserScroll)
        container.removeEventListener('touchmove', handleUserScroll)
      }
    }
  }, [containerRef.current])

  return (
    <>
      <BasicSearchLayout
        ref={menuRef}
        className={'flex h-screen-minus-header flex-col justify-between overflow-auto'}>
        {loading ? (
          <PageSkeleton />
        ) : (
          <>
            {showProgress && (
              <div className='mb-1 flex h-14 items-center justify-between bg-[#DFE8FF] px-6 py-3'>
                <div className='flex items-center'>
                  <Image
                    src='/images/searchloading.svg'
                    alt='Search loading icon'
                    width={16}
                    height={16}
                  />
                  <div className='ml-3'>
                    <Text type={TextEnum.Body_big}>{t('homePage.deepSearchProgressDesc')}</Text>
                    {false && (
                      <Text type={TextEnum.Body_big}>{t('homePage.deepSearchCompleted')}</Text>
                    )}
                  </div>
                  <div className='mr-1 flex items-center text-primary'>
                    <span>{'1/4'}</span>
                    <span>
                      <Text type={TextEnum.Body_big}>{t('homePage.checkDeepSearchResult')}</Text>
                    </span>
                    <ChevronRightIcon width={16} height={16} />
                  </div>
                  <div>...</div>
                </div>
                <div className='flex items-center'>
                  <div
                    className='mr-6 flex cursor-pointer items-center text-primary'
                    onClick={() => {}}>
                    <Image
                      src='/images/searchnotification.svg'
                      alt='Search notification icon'
                      width={12}
                      height={12}
                      className='mr-1.5'
                    />
                    <Text type={TextEnum.Body_small}>{t('homePage.sendNotification')}</Text>
                  </div>
                  <div>
                    <XMarkIcon
                      className='cursor-pointer'
                      width={24}
                      height={24}
                      onClick={() => {
                        setShowProgress(false)
                      }}
                    />
                  </div>
                </div>
              </div>
            )}
            <div
              id='ai-smarties-chat-content'
              ref={containerRef}
              className='flex-h-center hide-scrollbar flex-1 gap-2 overflow-auto px-14 pt-8'>
              <div className='shrink-1 relative ml-16 min-w-[692px] max-w-[760px]'>
                <div
                  className={clsx(
                    'absolute right-0 top-1 z-20',
                    type && (type as string).toUpperCase() === PoTypeEnum.GENERAL
                      ? 'top-[17px]'
                      : '',
                  )}>
                  {showShareButton && (
                    <ShareLink
                      shareType={(type as string).toUpperCase() as ShareTypeEnum}
                      buttonType='default'
                    />
                  )}
                </div>
                {type && (type as string).toUpperCase() !== PoTypeEnum.GENERAL && (
                  <Text type={TextEnum.H3} className='mb-5'>
                    {chatTitle}
                  </Text>
                )}
                <div>
                  {messages.length > 0 &&
                    messages.map((item, index) => {
                      const ref =
                        item.role === MessageRoleEnum.USER &&
                        (index === messages.length - 1 || index === messages.length - 2)
                          ? lastUserQuestionRef
                          : null

                      return (
                        <div key={index}>
                          <div className={clsx('relative pl-14', index === 0 ? 'pt-3' : 'pt-8')}>
                            <div
                              className={clsx(
                                'absolute left-3 top-3',
                                index === 0 ? 'pt-2' : 'top-8',
                              )}>
                              <Image
                                src={
                                  item.role === MessageRoleEnum.ASSISTANT
                                    ? '/images/system_avatar.svg'
                                    : '/images/default_avatar.svg'
                                }
                                width={32}
                                height={32}
                                className='mr-3 rounded-lg'
                                alt={'avatar'}
                              />
                            </div>
                            {item.role == MessageRoleEnum.ASSISTANT && item.reasoning && (
                              <>
                                <div className='flex items-center'>
                                  <div
                                    className='flex cursor-pointer items-center justify-center rounded-sm border bg-card px-3 py-2 hover:bg-slate-100'
                                    onClick={() => {
                                      handleExpandReasoning(item.processId)
                                    }}>
                                    <div className='text-primary'>
                                      <DeepSearchIcon />
                                    </div>
                                    <Text type={TextEnum.Body_medium} className='ml-1.5'>
                                      {t('homePage.reasoning')}
                                    </Text>
                                    {expendReasoning.find(
                                      (expendItem) => expendItem.processId === item.processId,
                                    )?.expand ? (
                                      <ChevronDownIcon className='ml-1' width={16} height={16} />
                                    ) : (
                                      <ChevronUpIcon className='ml-1' width={16} height={16} />
                                    )}
                                  </div>
                                  {/* <div
                                    className='ml-[14px] flex cursor-pointer items-center text-primary'
                                    onClick={requestPermission}>
                                    <Image
                                      src='/images/searchnotification.svg'
                                      alt='Search notification icon'
                                      width={12}
                                      height={12}
                                      className='mr-1.5'
                                    />
                                    <Text type={TextEnum.Body_small}>
                                      {t('homePage.sendNotification')}
                                    </Text>
                                  </div> */}
                                </div>
                                {expendReasoning.find(
                                  (expendItem) => expendItem.processId === item.processId,
                                )?.expand && (
                                  <div className='mb-7 mt-5 border-l-2 pl-4 text-secondary-black-3'>
                                    <Text type={TextEnum.Body_medium}>
                                      <MarkdownParser content={item.reasoning || ''} />
                                    </Text>
                                    {!item.content && (
                                      <div className='mt-3'>
                                        <LoadingDots size={4} />
                                      </div>
                                    )}
                                  </div>
                                )}
                              </>
                            )}
                            {item.role === MessageRoleEnum.USER ? (
                              index === 0 ? (
                                <div>
                                  <QuestionEditor
                                    type={(type as string).toUpperCase() as PoTypeEnum}
                                    disabled={disabledEdit}
                                    messages={item.content}
                                    onChange={handleUpdateQuestion}
                                    onCancel={handleCancelEdit}
                                    onSubmit={handleSubmitEdit}
                                    ref={ref}
                                  />
                                </div>
                              ) : (
                                <div ref={ref}>
                                  <MarkdownParser content={item.content[0].value} />
                                </div>
                              )
                            ) : (
                              <div className='w-[calc(100%-32px)] flex-1'>
                                <MarkdownParser content={item.content} list={item.referenceList} />
                                {isShowReference && (
                                  <>
                                    <ReferenceList referenceList={item.referenceList} />
                                    <Copy
                                      question={
                                        index === 1
                                          ? messages[0].content
                                          : (messages[index - 1] as UserMessage).content[0].value
                                      }
                                      sourceType={SourceTypeEnum.CHAT}
                                      sourceFromId={sessionId as string}
                                      content={item.content + joinReference(item.referenceList)}
                                      sourceId={item.reportSourceId}
                                      procId={item.processId}
                                    />
                                  </>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      )
                    })}

                  {pollingProcess && !isProcessFinished(pollingProcess.status) && (
                    <div className='flex p-3 pt-8'>
                      <div className='shrink-0'>
                        <Image
                          src='/images/system_avatar.svg'
                          width={32}
                          height={32}
                          className='mr-3 rounded-lg'
                          alt={'avatar'}
                        />
                      </div>
                      <InProgressProcess
                        process={pollingProcess}
                        className='mt-1'
                        isFirstQuery={isFirstQuery}
                      />
                    </div>
                  )}
                  {queryFailed && (
                    <div className='ml-3 mt-11 flex'>
                      <div className='shrink-0'>
                        <Image
                          src='/images/system_avatar.svg'
                          width={32}
                          height={32}
                          className='mr-3 rounded-lg'
                          alt={'avatar'}
                        />
                      </div>
                      <Text type={TextEnum.Body_medium} className='text-dangerous-main mt-2'>
                        {t('global:error.systemError')}
                      </Text>
                    </div>
                  )}
                </div>
              </div>
              <div className={clsx('ml-6 w-80 shrink-0')}>
                <Recommend
                  searchingPdf={pdfSearching}
                  searchingImg={imgsSearching}
                  pdfs={pdfs}
                  imgs={imgs}
                  onCloseModal={updateCredits}
                  processId={pdfProcessId}
                />
              </div>
            </div>
            <ChatFooter
              type={(type as string).toLocaleUpperCase() ?? ''}
              onSearch={handleSendQuery}
              showStop={showStop}
              disabled={disableBottomInput}
              onStop={() => {
                setOpenStopModal(true)
              }}
            />
            <SelectDropdown
              onSearchLoading={setLoading}
              messages={messages}
              type='chat'
              sourceId={sessionId}
            />
          </>
        )}
      </BasicSearchLayout>
      <StopModal
        open={openStopModal}
        onClose={() => {
          setOpenStopModal(false)
        }}
        onConfirm={handleCancelQuery}
      />
      <CreditsModal
        open={openCreditsModal}
        onClose={() => {
          setOpenCreditsModal(false)
        }}
      />
    </>
  )
}

export default SearchChatPage
