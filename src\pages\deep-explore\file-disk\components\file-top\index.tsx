import { uploadFileApi } from '@/api/pdfs/uploadFiles'
import { Text, TextEnum } from '@/components/business/text'
import { ImportFilesModal } from '@/components/business/upload/ImportFilesModal'
import { Button } from '@/components/ui/button'
import { Namespace } from '@/i18n'
import { ArrowUpTrayIcon } from '@heroicons/react/24/outline'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'sonner'
import { useFile } from '../../store/useFile'
import { RetrievalOperation } from './RetrievalOperation'
export const FileTop: React.FC<> = () => {
  const { t } = useTranslation([Namespace.DEEPEXPLORE, Namespace.GLOBAL])
  const [visibleFile, setVisibleFile] = useState(false)
  const totalSize = useFile((f) => f.totalSize)
  const maxSize = useFile((f) => f.maxSize)
  const setSearch = useFile((f) => f.setSearch)

  const handleUploadFiles = async (urlList: string[]) => {
    const uploadPromises = urlList.map(({ name, s3Key, size }) => {
      return uploadFileApi({
        fileName: name,
        s3Key,
        fileSize: size,
      })
        .then(() => ({
          status: 'fulfilled',
          key: s3Key,
          name,
        }))
        .catch((error) => ({
          status: 'rejected',
          key: s3Key,
          name,
          error,
        }))
    })

    const results = await Promise.all(uploadPromises)

    const failed = results.filter((r) => r.status === 'rejected')
    const succeeded = results.filter((r) => r.status === 'fulfilled')

    if (failed.length) {
      toast.error(
        <div>
          <Text type={TextEnum.Body_big}>{t('importFile.upload.followingUploadFailed')}</Text>
          <div>
            {failed.map((f) => (
              <div key={f.key} className='mt-2'>
                {f.name}
              </div>
            ))}
          </div>
        </div>,
        {
          duration: 10000,
        },
      )
    }

    if (succeeded.length) {
      setSearch({
        lastPageKey: null,
      })
    }

    setVisibleFile(false)
  }

  return (
    <div className='px-[158px]'>
      {/* 上传 */}
      <div className='flex select-none items-center justify-between'>
        <Button
          className='rounded-sm'
          onClick={() => {
            setVisibleFile(true)
          }}>
          <ArrowUpTrayIcon className='mr-1 h-4 w-4' />
          <Text type={TextEnum.Body_medium}> {t('importFile.button')} </Text>
        </Button>
        {/* 筛选操作 */}
        <RetrievalOperation />
      </div>

      <ImportFilesModal
        open={visibleFile}
        onClose={() => setVisibleFile(false)}
        isShowUrl={false}
        isCapacityExceeded={totalSize >= maxSize}
        onUploadFiles={handleUploadFiles}
      />
    </div>
  )
}
