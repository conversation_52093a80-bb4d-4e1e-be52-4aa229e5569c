import { XMarkIcon, ArrowLeftIcon, ArrowRightIcon } from '@heroicons/react/24/outline'
import Image from 'next/image'

import { useEffect, useRef, useState } from 'react'
import { Modal } from '../../modal'
import clsx from 'clsx'
import ImgChat from './ImgChat'
import { MediaItemType } from '@/api/getMedia'
import { ArrowTopRightOnSquareIcon } from '@heroicons/react/24/solid'
import { TextEnum, Text } from '../../text'
import style from './ImgPreview.module.scss'

export interface CircelArrowProps {
  type: 'left' | 'right'
  disabled: boolean
}
const CircelArrow: React.FC<CircelArrowProps> = ({ type, disabled }: CircelArrowProps) => {
  return (
    <div
      className={clsx(
        'flex-center absolute top-[50%] h-12 w-12 rounded-full bg-black-1-50',
        type === 'left' ? 'left-2' : 'right-2',
        disabled ? 'cursor-not-allowed' : 'cursor-pointer hover:bg-black-1',
      )}>
      {type === 'left' && (
        <ArrowLeftIcon
          className={clsx('h-4 w-4', disabled ? 'text-black-2' : 'text-primary-foreground')}
        />
      )}
      {type === 'right' && (
        <ArrowRightIcon
          className={clsx('h-4 w-4', disabled ? 'text-black-2' : 'text-primary-foreground')}
        />
      )}
    </div>
  )
}

export interface ImgPreviewProps {
  imgs: Array<MediaItemType>
  open: boolean
  onClose: () => void
  clickedIndex: number
}
const ImgPreview: React.FC<ImgPreviewProps> = ({
  imgs,
  open,
  onClose,
  clickedIndex,
}: ImgPreviewProps) => {
  const thumbContainerRef = useRef<HTMLDivElement>(null)

  const [previewIndex, setPreviewIndex] = useState(clickedIndex)

  const [disableThumbLeft, setDisableThumbLeft] = useState(false)
  const [disableThumbRight, setDisableThumbRight] = useState(false)
  const [disablePreviewLeft, setDisablePreviewLeft] = useState(false)
  const [disablePreviewRight, setDisablePreviewRight] = useState(false)

  useEffect(() => {
    setPreviewIndex(previewIndex)

    setDisablePreviewLeft(previewIndex === 0)
    setDisablePreviewRight(previewIndex === imgs.length - 1)

    if (!thumbContainerRef.current) return
    const container = thumbContainerRef.current

    const maxScrollLeft = container.scrollWidth - container.offsetWidth
    setDisableThumbLeft(container.scrollLeft === 0)
    setDisableThumbRight(container.scrollLeft >= maxScrollLeft)
  }, [imgs, previewIndex])

  useEffect(() => {
    if (open) {
      // reset
      setPreviewIndex(clickedIndex)
    }
  }, [open])

  const handleClickThumb = (index: number) => {
    setPreviewIndex(index)
    // 确保点击的缩略图完全可见
    if (!thumbContainerRef.current) return
    if (thumbContainerRef.current) {
      const container = thumbContainerRef.current
      const thumbWidth = 132 // 每个缩略图的宽度（含间距）
      const visibleStart = container.scrollLeft
      const visibleEnd = visibleStart + container.offsetWidth

      const thumbStart = index * thumbWidth
      const thumbEnd = thumbStart + thumbWidth

      // 如果缩略图右侧部分超出视图范围，向右滚动
      if (thumbEnd > visibleEnd) {
        container.scrollTo({
          left: thumbEnd - container.offsetWidth,
          behavior: 'smooth',
        })
      }
      // 如果缩略图左侧部分超出视图范围，向左滚动
      else if (thumbStart < visibleStart) {
        container.scrollTo({
          left: thumbStart,
          behavior: 'smooth',
        })
      }
    }
  }

  const handleMoveThumbList = (type: 'left' | 'right') => {
    if (!thumbContainerRef.current) return
    const container = thumbContainerRef.current

    // const thumbWidth = 132 // 每个缩略图宽度 (包括间隙)
    const containerVisibleWidth = container.offsetWidth

    const maxScrollLeft = container.scrollWidth - containerVisibleWidth

    let newScrollLeft: number
    if (type === 'left') {
      newScrollLeft = Math.max(container.scrollLeft - containerVisibleWidth, 0)
    } else {
      newScrollLeft = Math.min(container.scrollLeft + containerVisibleWidth, maxScrollLeft)
    }

    container.scrollTo({
      left: newScrollLeft,
      behavior: 'smooth',
    })

    // 设置按钮的禁用状态
    setDisableThumbLeft(newScrollLeft === 0)
    setDisableThumbRight(newScrollLeft >= maxScrollLeft)
  }

  const handleClickPreviewImg = (type: 'left' | 'right') => {
    if (disablePreviewLeft && type === 'left') return
    if (disablePreviewRight && type === 'right') return

    let newIndex: number
    if (type === 'left') {
      newIndex = Math.max(previewIndex - 1, 0)
    } else {
      newIndex = Math.min(previewIndex + 1, imgs.length - 1)
    }
    setPreviewIndex(newIndex)

    // 检查缩略图是否在视图内
    if (thumbContainerRef.current) {
      const container = thumbContainerRef.current
      const thumbWidth = 132 // 每个缩略图的宽度（含间距）
      const visibleStart = container.scrollLeft
      const visibleEnd = visibleStart + container.offsetWidth

      const thumbStart = newIndex * thumbWidth
      const thumbEnd = thumbStart + thumbWidth

      // 如果缩略图右侧部分超出视图范围，向右滚动
      if (thumbEnd > visibleEnd) {
        container.scrollTo({
          left: thumbEnd - container.offsetWidth,
          behavior: 'smooth',
        })
      }
      // 如果缩略图左侧部分超出视图范围，向左滚动
      else if (thumbStart < visibleStart) {
        container.scrollTo({
          left: thumbStart,
          behavior: 'smooth',
        })
      }
    }
  }

  const handleClickImg = (url: string) => {
    if (url) {
      window.open(url, '_blank')
    }
  }

  return (
    <>
      <Modal open={open} onClose={onClose} overlayClassName={'!bg-black-1'}>
        <div className='h-screen min-h-96 w-screen min-w-[700px] bg-black-1 px-6 pb-6'>
          <div className='flex-v-center h-16 w-full justify-end'>
            <XMarkIcon color='#fff' className='mr-1 h-5 w-5 cursor-pointer' onClick={onClose} />
          </div>
          <div
            className={clsx(
              'box-border flex w-full flex-row gap-4 rounded-t-sm bg-card p-4',
              style['h-modal'],
            )}>
            <div className='h-screen-minus-header w-[calc(70%-16px)] shrink-0'>
              <div className='relative w-full rounded-t-sm bg-gray-imgBg px-9' key={previewIndex}>
                <div className={clsx('w-ful relative h-full', style['h-img'])}>
                  <Image
                    src={imgs[previewIndex]?.s3Url ?? imgs[previewIndex]?.mediaUrl}
                    alt={`image-${previewIndex}`}
                    fill
                    sizes='896px'
                    className='rounded-sm'
                    style={{
                      objectFit: 'scale-down',
                    }}
                    priority
                    quality={100}
                  />
                </div>
                <div
                  onClick={() => {
                    handleClickPreviewImg('left')
                  }}>
                  <CircelArrow type='left' disabled={disablePreviewLeft} />
                </div>
                <div
                  onClick={() => {
                    handleClickPreviewImg('right')
                  }}>
                  <CircelArrow type='right' disabled={disablePreviewRight} />
                </div>
                {imgs[previewIndex]?.siteUrl && (
                  <div
                    className='absolute bottom-3 left-3 flex h-8 max-w-[320px] cursor-pointer flex-row items-center gap-1 rounded-sm border border-border bg-card p-2'
                    onClick={() => {
                      handleClickImg(imgs[previewIndex]?.siteUrl)
                    }}>
                    <Text
                      type={TextEnum.Body_medium}
                      className={'overflow-hidden text-ellipsis whitespace-nowrap'}>
                      {imgs[previewIndex]?.siteUrl}
                    </Text>
                    <ArrowTopRightOnSquareIcon className='h-4 w-4 shrink-0 text-secondary-foreground' />
                  </div>
                )}
              </div>
              <div className='flex-center mt-2 w-full'>
                <div
                  onClick={() => {
                    handleMoveThumbList('left')
                  }}
                  className={clsx(
                    'flex-center mr-2 h-20 w-8',
                    disableThumbLeft
                      ? 'cursor-not-allowed hover:bg-card'
                      : 'cursor-pointer hover:bg-secondary',
                  )}>
                  <ArrowLeftIcon
                    className={clsx(
                      'h-4 w-4 shrink-0',
                      disableThumbLeft ? 'text-secondary-black-2' : 'text-secondary-black-3',
                    )}
                  />
                </div>

                <div
                  className='hide-scrollbar flex flex-1 gap-3 overflow-x-scroll'
                  ref={thumbContainerRef}>
                  {imgs.map((item, index) => (
                    <div
                      className={clsx(
                        'relative h-20 w-[120px] shrink-0 rounded-md border bg-gray-imgBg',
                        previewIndex === index ? 'rounded-md border-2 border-primary' : '',
                      )}
                      key={index}
                      onClick={() => {
                        handleClickThumb(index)
                      }}>
                      <Image
                        src={item?.s3Url ?? item.mediaUrl}
                        alt={`image-${index}`}
                        fill
                        sizes='120px'
                        className='cursor-pointer rounded-md'
                      />
                    </div>
                  ))}
                </div>
                <div
                  className={clsx(
                    'flex-center ml-2 h-20 w-8',
                    disableThumbRight
                      ? 'cursor-not-allowed hover:bg-card'
                      : 'cursor-pointer hover:bg-secondary',
                  )}
                  onClick={() => {
                    handleMoveThumbList('right')
                  }}>
                  <ArrowRightIcon
                    className={clsx(
                      'h-4 w-4 shrink-0',
                      disableThumbRight ? 'text-secondary-black-2' : 'text-secondary-black-3',
                    )}
                  />
                </div>
              </div>
            </div>
            <div className='ml-4 w-[calc(30%-16px)] min-w-[200px] shrink-0 rounded-md bg-background'>
              <ImgChat img={imgs[previewIndex]} />
            </div>
          </div>
        </div>
      </Modal>
    </>
  )
}

export default ImgPreview
