import { ArrowDownTrayIcon } from '@heroicons/react/24/outline'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { getInvoice } from '@/api/subscription/getInvoice'
import { ListTransactionItem, TransactionStatus } from '@/api/subscription/listTransaction'
import { Namespace, SupportedLangs } from '@/i18n'
import { useTranslation } from 'react-i18next'
import { SubscriptionEnum } from '@/types'
import { formatTime } from '../utils/formatTime'
import { useUserStore } from '@/store/userStore'

interface TransTableProps {
  transactions: ListTransactionItem[]
}

export function TransTable({ transactions }: TransTableProps) {
  const { t } = useTranslation(Namespace.SUBSCRIPTION)
  const user = useUserStore((state) => state.user)

  const handleDownload = async (id: string) => {
    const res = await getInvoice({
      transactionId: id,
    })
    if (res.url) {
      window.open(res.url, '_blank')
    }
  }

  const renderStatus = (status: TransactionStatus) => {
    switch (status) {
      case TransactionStatus.paid:
      case TransactionStatus.completed:
        return (
          <span className='rounded-2xl bg-success-10 px-2 py-[2px] text-sl text-success'>
            {t('table.status.success')}
          </span>
        )
      default:
        return (
          <span className='rounded-2xl bg-dangerous-10 px-2 py-[2px] text-sl text-dangerous'>
            {t('table.status.failed')}
          </span>
        )
    }
  }

  return (
    <Table className='min-w-[1014px] border-b'>
      <TableHeader className='text-foreground'>
        <TableRow className='bg-gray-tabHeader text-base text-secondary-black-3'>
          <TableHead>{t('table.header.date')}</TableHead>
          <TableHead>{t('table.header.plan')}</TableHead>
          <TableHead>{t('table.header.status')}</TableHead>
          {/* <TableHead className='text-right'>{t('table.header.invoiceTotal')}</TableHead> */}
          <TableHead className='w-20 text-right'>{t('table.header.invoice')}</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {transactions.map((transaction, index) => (
          <TableRow key={index} className='leading-4'>
            <TableCell className='px-5 py-7'>
              {formatTime(transaction.createdAt, user?.setting.language as SupportedLangs)}
            </TableCell>
            <TableCell className='px-5 py-7'>
              {transaction.plan === SubscriptionEnum.BASIC_MONTH ||
              transaction.plan === SubscriptionEnum.BASIC_YEAR
                ? t('table.basicPlan')
                : t('table.proPlan')}
            </TableCell>
            <TableCell className='h-full px-5'>{renderStatus(transaction.status)}</TableCell>
            {/* <TableCell className='px-5 text-right text-secondary-black-2'>
              {transaction.priceTotal}&nbsp;{transaction.priceCurrency}
            </TableCell> */}
            <TableCell className='cursor-pointer px-5 text-right'>
              {/* {(transaction.status === TransactionStatus.completed ||
                transaction.status === TransactionStatus.paid) &&
                transaction.priceTotal !== '0' && (
                  <div
                    className='inline-block w-fit rounded-md border border-border px-5 py-2.5 hover:border-none hover:bg-primary-hover'
                    onClick={() => {
                      handleDownload(transaction.transactionId)
                    }}>
                    <ArrowDownTrayIcon className='h-5 w-5' />
                  </div>
                )} */}
              {(transaction.status === TransactionStatus.completed ||
                transaction.status === TransactionStatus.paid) && (
                <div
                  className='inline-block w-fit rounded-md border border-border px-5 py-2.5 hover:border-primary-hover hover:bg-primary-hover'
                  onClick={() => {
                    handleDownload(transaction.transactionId)
                  }}>
                  <ArrowDownTrayIcon className='h-5 w-5' />
                </div>
              )}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
