import { Modal } from '@/components/business/modal'
import { Button } from '@/components/ui/button'
import { Text, TextEnum } from '@/components/business/text'
import { Textinput } from '@/components/business/text-input'
import { XMarkIcon } from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import { useEffect, useState } from 'react'
import { useGenerate } from '../../store/useGenerate'
import { useReport } from '../../store/useReport'
import { ReportTypeEnum } from '../../hooks/useDocEmpty'
import { useReportList } from '../../store/useReportList'

export const ModalDesc = ({ open, handleCancel, info }) => {
  const updateDocument = useGenerate((s) => s.updateDocument)
  const setDescriptionLoading = useGenerate((s) => s.setDescriptionLoading)
  const currentReport = useReport((s) => s.currentReport)
  const updateCurrentReport = useReport((s) => s.updateCurrentReport)
  const updateReportList = useReportList((s) => s.updateReportList)
  const reportList = useReportList((s) => s.reportList)
  const { t } = useTranslation([Namespace.GLOBAL, Namespace.GENERATOR])
  const [inputValue, setInputValue] = useState('')
  const onChangeTextInput = (e) => {
    setInputValue(e.target.value)
  }
  useEffect(() => {
    if (open) {
      setInputValue('')
    }
  }, [open])
  const onSetDesc = async () => {
    setDescriptionLoading(true)
    const titleMap = {
      [ReportTypeEnum.EMPTY]: inputValue,
      [ReportTypeEnum.BUSINESS_PLAN]: inputValue + t('generator:businessPlan'),
      [ReportTypeEnum.COMPANY]: inputValue + t('generator:companyAnalysis'),
    }
    const query = {
      description: inputValue,
      type: info.type,
      reportId: currentReport.reportId,
      title: titleMap[info.type],
    }
    await updateDocument(query)
    updateCurrentReport({ ...query, content: '' })
    // 更新列表
    const list = reportList.map((r) =>
      r.reportId === currentReport.reportId ? { ...r, title: query.title } : r,
    )
    updateReportList(list)
    handleCancel()
    setDescriptionLoading(false)
  }
  return (
    <Modal open={open} onClose={handleCancel} className='w-[434px] break-all p-6'>
      <div>
        <Text type={TextEnum.H4} className='flex items-center justify-between'>
          {info.name}
          <XMarkIcon onClick={handleCancel} className='h-5 w-5 cursor-pointer' />
        </Text>
        <div className='mt-4'>
          <Text type={TextEnum.Body_medium} className='mb-2.5'>
            {t('generator:description')}
          </Text>
          <Textinput
            placeholder={t('generator:descriptionPlaceholder')}
            maxLength={100}
            isStopEnterPrevent={false}
            className='mb-3 mt-1 border-border text-sm'
            value={inputValue}
            onChange={onChangeTextInput}
          />
          <div className='flex justify-end'>
            <Button onClick={onSetDesc} disabled={!inputValue} className='ml-2'>
              {t('button.confirm')}
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  )
}
