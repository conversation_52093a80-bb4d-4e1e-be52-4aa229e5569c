import { fetchWithAuth } from '@/lib/fetch'
import { SupportedLangs } from '@/i18n'
import { AuthError } from './types'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface ForgotPasswordRequest {
  email: string
  language: SupportedLangs
}

export interface ForgotPasswordResponse {
  result: string
}

export const forgotPassword = (data: ForgotPasswordRequest) => {
  return fetchWithAuth<ForgotPasswordResponse | AuthError>(`${apiUrl}/auth/forgotPassword`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })
}
