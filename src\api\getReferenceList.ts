import { fetchWithAuth } from '@/lib/fetch'

interface GetReferenceListRequest {
  sourceId: string
}

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export const getReferenceList = async (data: GetReferenceListRequest) => {
  const response = await fetchWithAuth(`${apiUrl}/report/getReferenceList`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
