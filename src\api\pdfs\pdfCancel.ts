import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface PdfCancelRequest {
  pdfProcessId: string
}

export type PdfCancelResponse = {
  pdfProcess: {
    status: 'cancelled'
  }
}

export const pdfCancel = async (data: PdfCancelRequest): Promise<PdfCancelResponse> => {
  const response = await fetchWithAuth<PdfCancelResponse>(`${apiUrl}/pdf/cancel`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
