# How to implement i18n

## 1. namespace

pages should be divided by namespace, all the common translation should put into common part.

Don't worry about duplicate in different namespace.

## 2. the language could be set in cookie

like lang = en

## 3. resource

if the application is not very large, it is not necessary to divide resource.(less than 1 image and js can be cached)

So divided it when necessary.

## 4. all the resource should be imported by components

For example in CN folder, it should only import the resource from other component folder:

```ts
import component1 from '../components/component1/i18n/cn'

export default {
  component1,
}
```

## 5. LanguageSelect

when this component is imported, it will automatically do browser-side rendering.

when select language is not necessary just use the useLanguage part
