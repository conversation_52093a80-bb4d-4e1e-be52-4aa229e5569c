import { fetchWithAuth } from '@/lib/fetch'
import { SubscriptionEnum } from '@/types'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export enum TransactionStatus {
  paid = 'paid',
  completed = 'completed',
  past_due = 'past_due',
}
export interface ListTransactionItem {
  transactionId: string
  createdAt: number
  priceTotal: string
  priceCurrency: string
  status: TransactionStatus
  plan: SubscriptionEnum
}

export type ListTransactionResponse = ListTransactionItem[]

export const listTransaction = async (): Promise<ListTransactionResponse> => {
  const response = await fetchWithAuth<ListTransactionResponse>(`${apiUrl}/transaction/list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  })

  return response
}
