import { OutlineItem } from '@/store/deep-research'

/**
 * 拖拽状态接口
 */
export interface DragState {
  draggedItem: OutlineItem | null
  draggedPath: number[] | null
  dropTargetPath: number[] | null
  dropPosition: 'before' | 'after' | 'inside' | null
  isDragging: boolean
}

/**
 * 拖拽位置类型
 */
export type DropPosition = 'before' | 'after' | 'inside'

/**
 * 拖拽数据传输接口
 */
export interface DragTransferData {
  itemId: string
  sourcePath: number[]
}

/**
 * 大纲项目渲染属性
 */
export interface OutlineItemProps {
  item: OutlineItem
  isShowDescription: boolean
  isLast?: boolean
  level?: number
  path?: number[]
  expandedItems: Set<string>
  onToggleExpand: (sequence: string) => void
  onUpdateItem: (itemId: string, field: 'title' | 'description', value: string) => void
  onDeleteChapter?: (path: number[]) => void
  onAddChapter?: (path: number[]) => void
  onAddSubChapter?: (path: number[]) => void
}

/**
 * 可拖拽大纲项目属性
 */
export interface DraggableOutlineItemProps {
  item: OutlineItem
  path: number[]
  level: number
  expandedItems: Set<string>
  dragState: DragState
  onToggleExpand: (sequence: string) => void
  onDragStart: (e: React.DragEvent, item: OutlineItem, path: number[]) => void
  onDragOver: (e: React.DragEvent, targetPath: number[], position: DropPosition) => void
  onDragLeave: (e: React.DragEvent) => void
  onDrop: (e: React.DragEvent, targetPath: number[], position: DropPosition) => void
  onDragEnd: (e: React.DragEvent) => void
}

/**
 * 大纲编辑器属性
 */
export interface OutlineEditorProps {
  outline: OutlineItem[]
  showDescriptions: boolean
  expandedItems: Set<string>
  onToggleExpand: (sequence: string) => void
  onUpdateItem: (itemId: string, field: 'title' | 'description', value: string) => void
  onDeleteChapter: (path: number[]) => void
  onAddChapter: (path: number[]) => void
  onAddSubChapter: (path: number[]) => void
}

/**
 * 大纲预览属性
 */
export interface OutlinePreviewProps {
  outline: OutlineItem[]
  expandedItems: Set<string>
  dragState: DragState
  onToggleExpand: (sequence: string) => void
  onDragStart: (e: React.DragEvent, item: OutlineItem, path: number[]) => void
  onDragOver: (e: React.DragEvent, targetPath: number[], position: DropPosition) => void
  onDragLeave: (e: React.DragEvent) => void
  onDrop: (e: React.DragEvent, targetPath: number[], position: DropPosition) => void
  onDragEnd: (e: React.DragEvent) => void
}

/**
 * 模态框属性
 */
export interface ModalOutlineProps {
  open: boolean
  handleCancel: () => void
}

/**
 * 拖拽钩子返回值
 */
export interface UseDragDropReturn {
  dragState: DragState
  handleDragStart: (e: React.DragEvent, item: OutlineItem, path: number[]) => void
  handleDragOver: (e: React.DragEvent, targetPath: number[], position: DropPosition) => void
  handleDragLeave: (e: React.DragEvent) => void
  handleDrop: (e: React.DragEvent, targetPath: number[], position: DropPosition) => void
  handleDragEnd: (e: React.DragEvent) => void
  cleanupDragState: () => void
}

/**
 * 大纲管理钩子返回值
 */
export interface UseOutlineManagementReturn {
  expandedItems: Set<string>
  showDescriptions: boolean
  toggleExpand: (sequence: string) => void
  toggleExpandAll: () => void
  toggleShowDescriptions: () => void
  updateOutlineItem: (itemId: string, field: 'title' | 'description', value: string) => void
  deleteChapter: (path: number[]) => void
  addChapter: (path: number[]) => void
  addSubChapter: (path: number[]) => void
  collectAllSequences: (items: OutlineItem[]) => string[]
}
