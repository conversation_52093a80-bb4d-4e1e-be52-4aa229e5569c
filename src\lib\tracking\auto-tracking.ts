import { TrackingEventType, TrackingProperties } from './types'
import { executeCustomMethod, getCommonProperties, parseElementTrackingInfo } from './common'
import mixpanel from 'mixpanel-browser'
import { trackingHighlight } from './tracking-highlight'

/**
 * 自动追踪功能实现
 */
export const autoTracking = {
  // 已处理的VIEW事件元素集合
  processedViewElements: new WeakSet<HTMLElement>(),

  // 已处理的HOVER事件元素集合（防止重复触发）
  processedHoverElements: new WeakSet<HTMLElement>(),

  /**
   * 初始化自动追踪
   */
  init() {
    if (typeof window === 'undefined') return

    // 初始化时处理所有已存在的VIEW事件
    this.handleViewEvents()

    // 添加全局事件监听器
    document.addEventListener('click', this.handleClickEvents.bind(this), true)
    document.addEventListener('mouseover', this.handleHoverEvents.bind(this), true)
    document.addEventListener('submit', this.handleSubmitEvents.bind(this), true)
    document.addEventListener('change', this.handleChangeEvents.bind(this), true)
    document.addEventListener('focus', this.handleFocusEvents.bind(this), true)
    document.addEventListener('blur', this.handleBlurEvents.bind(this), true)
    document.addEventListener('keypress', this.handleKeyPressEvents.bind(this), true)

    // 初始化追踪高亮功能
    trackingHighlight.init()

    // 使用MutationObserver监听DOM变化，处理新添加的元素
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // 检查新添加的元素是否需要VIEW事件追踪
              this.checkViewTracking(node as HTMLElement)

              // 检查子元素
              const elements = (node as HTMLElement).querySelectorAll('*')
              elements.forEach((element) => {
                this.checkViewTracking(element as HTMLElement)
              })
            }
          })
        }
      })
    })

    // 开始观察整个文档
    observer.observe(document.body, {
      childList: true,
      subtree: true,
    })

    if (process.env.NODE_ENV !== 'production') {
      console.log('[Auto-Tracking] Initialized')
    }
  },

  /**
   * 处理所有VIEW类型的事件
   */
  handleViewEvents() {
    // 查找所有带有asm-tracking属性的元素
    const elements = document.querySelectorAll('[asm-tracking]')

    elements.forEach((element) => {
      this.checkViewTracking(element as HTMLElement)
    })
  },

  /**
   * 检查元素是否需要VIEW事件追踪
   */
  async checkViewTracking(element: HTMLElement) {
    // 如果已经处理过，跳过
    if (this.processedViewElements.has(element)) return

    const trackingInfo = parseElementTrackingInfo(element)
    if (!trackingInfo) return

    // 如果是VIEW类型事件，立即触发
    if (trackingInfo.eventType === TrackingEventType.VIEW) {
      // 标记为已处理
      this.processedViewElements.add(element)

      // 获取通用属性和自定义参数
      let properties: TrackingProperties = {
        ...(await getCommonProperties()),
        ...trackingInfo.params,
      }

      // 如果有自定义方法，执行它
      if (trackingInfo.customMethod) {
        properties = executeCustomMethod(trackingInfo.customMethod, properties)
      }

      // 发送到 Mixpanel
      mixpanel.track(trackingInfo.eventName, properties)

      // 开发环境下在控制台输出
      if (process.env.NODE_ENV !== 'production') {
        console.log(`[Auto-Tracking] VIEW Event:`, trackingInfo.eventName, properties)
      }
    }
  },

  /**
   * 处理点击事件
   */
  async handleClickEvents(event: MouseEvent) {
    const element = event.target as HTMLElement
    if (!element) return

    // 查找当前元素及其所有父元素中是否有需要追踪的
    let currentElement: HTMLElement | null = element

    while (currentElement) {
      const trackingInfo = parseElementTrackingInfo(currentElement)

      if (trackingInfo && trackingInfo.eventType === TrackingEventType.CLICK) {
        // 获取通用属性和自定义参数
        let properties: TrackingProperties = {
          ...(await getCommonProperties()),
          ...trackingInfo.params,
        }

        // 如果有自定义方法，执行它
        if (trackingInfo.customMethod) {
          properties = executeCustomMethod(trackingInfo.customMethod, properties)
        }

        // 发送到 Mixpanel
        mixpanel.track(trackingInfo.eventName, properties)

        // 开发环境下在控制台输出
        if (process.env.NODE_ENV !== 'production') {
          console.log(`[Auto-Tracking] CLICK Event:`, trackingInfo.eventName, properties)
        }

        // 找到一个可追踪的元素后就停止
        break
      }

      // 继续检查父元素
      currentElement = currentElement.parentElement
    }
  },

  /**
   * 处理悬停事件
   */
  async handleHoverEvents(event: MouseEvent) {
    const element = event.target as HTMLElement
    if (!element) return

    // 查找当前元素及其所有父元素中是否有需要追踪的
    let currentElement: HTMLElement | null = element

    while (currentElement) {
      // 如果已经处理过，跳过
      if (this.processedHoverElements.has(currentElement)) {
        currentElement = currentElement.parentElement
        continue
      }

      const trackingInfo = parseElementTrackingInfo(currentElement)

      if (trackingInfo && trackingInfo.eventType === TrackingEventType.HOVER) {
        // 标记为已处理（防止重复触发）
        this.processedHoverElements.add(currentElement)

        // 获取通用属性和自定义参数
        let properties: TrackingProperties = {
          ...(await getCommonProperties()),
          ...trackingInfo.params,
        }

        // 如果有自定义方法，执行它
        if (trackingInfo.customMethod) {
          properties = executeCustomMethod(trackingInfo.customMethod, properties)
        }

        // 发送到 Mixpanel
        mixpanel.track(trackingInfo.eventName, properties)

        // 开发环境下在控制台输出
        if (process.env.NODE_ENV !== 'production') {
          console.log(`[Auto-Tracking] HOVER Event:`, trackingInfo.eventName, properties)
        }

        // 设置一个定时器，在一段时间后允许再次触发
        setTimeout(() => {
          this.processedHoverElements.delete(currentElement as HTMLElement)
        }, 2000) // 2秒内不重复触发

        // 找到一个可追踪的元素后就停止
        break
      }

      // 继续检查父元素
      currentElement = currentElement.parentElement
    }
  },

  /**
   * 处理表单提交事件
   */
  async handleSubmitEvents(event: Event) {
    const form = event.target as HTMLFormElement
    if (!form || form.tagName !== 'FORM') return

    const trackingInfo = parseElementTrackingInfo(form)

    if (trackingInfo && trackingInfo.eventType === TrackingEventType.SUBMIT) {
      // 获取通用属性和自定义参数
      let properties: TrackingProperties = {
        ...(await getCommonProperties()),
        ...trackingInfo.params,
      }

      // 如果有自定义方法，执行它
      if (trackingInfo.customMethod) {
        properties = executeCustomMethod(trackingInfo.customMethod, properties)
      }

      // 发送到 Mixpanel
      mixpanel.track(trackingInfo.eventName, properties)

      // 开发环境下在控制台输出
      if (process.env.NODE_ENV !== 'production') {
        console.log(`[Auto-Tracking] SUBMIT Event:`, trackingInfo.eventName, properties)
      }
    }
  },

  /**
   * 处理输入变化事件
   */
  async handleChangeEvents(event: Event) {
    const element = event.target as HTMLElement
    if (!element) return

    const trackingInfo = parseElementTrackingInfo(element)

    if (trackingInfo && trackingInfo.eventType === TrackingEventType.CHANGE) {
      // 获取通用属性和自定义参数
      let properties: TrackingProperties = {
        ...(await getCommonProperties()),
        ...trackingInfo.params,
      }

      // 如果是输入元素，添加值（但不包含密码）
      if (
        element instanceof HTMLInputElement &&
        element.type !== 'password' &&
        element.type !== 'file'
      ) {
        properties.input_value = element.value
      }

      // 如果有自定义方法，执行它
      if (trackingInfo.customMethod) {
        properties = executeCustomMethod(trackingInfo.customMethod, properties)
      }

      // 发送到 Mixpanel
      mixpanel.track(trackingInfo.eventName, properties)

      // 开发环境下在控制台输出
      if (process.env.NODE_ENV !== 'production') {
        console.log(`[Auto-Tracking] CHANGE Event:`, trackingInfo.eventName, properties)
      }
    }
  },

  /**
   * 处理获取焦点事件
   */
  async handleFocusEvents(event: FocusEvent) {
    const element = event.target as HTMLElement
    if (!element) return

    const trackingInfo = parseElementTrackingInfo(element)

    if (trackingInfo && trackingInfo.eventType === TrackingEventType.FOCUS) {
      // 获取通用属性和自定义参数
      let properties: TrackingProperties = {
        ...(await getCommonProperties()),
        ...trackingInfo.params,
      }

      // 如果有自定义方法，执行它
      if (trackingInfo.customMethod) {
        properties = executeCustomMethod(trackingInfo.customMethod, properties)
      }

      // 发送到 Mixpanel
      mixpanel.track(trackingInfo.eventName, properties)

      // 开发环境下在控制台输出
      if (process.env.NODE_ENV !== 'production') {
        console.log(`[Auto-Tracking] FOCUS Event:`, trackingInfo.eventName, properties)
      }
    }
  },

  /**
   * 处理失去焦点事件
   */
  async handleBlurEvents(event: FocusEvent) {
    const element = event.target as HTMLElement
    if (!element) return

    const trackingInfo = parseElementTrackingInfo(element)

    if (trackingInfo && trackingInfo.eventType === TrackingEventType.BLUR) {
      // 获取通用属性和自定义参数
      let properties: TrackingProperties = {
        ...(await getCommonProperties()),
        ...trackingInfo.params,
      }

      // 如果有自定义方法，执行它
      if (trackingInfo.customMethod) {
        properties = executeCustomMethod(trackingInfo.customMethod, properties)
      }

      // 发送到 Mixpanel
      mixpanel.track(trackingInfo.eventName, properties)

      // 开发环境下在控制台输出
      if (process.env.NODE_ENV !== 'production') {
        console.log(`[Auto-Tracking] BLUR Event:`, trackingInfo.eventName, properties)
      }
    }
  },

  /**
   * 处理按键事件
   */
  async handleKeyPressEvents(event: KeyboardEvent) {
    const element = event.target as HTMLElement
    if (!element) return

    const trackingInfo = parseElementTrackingInfo(element)

    if (trackingInfo && trackingInfo.eventType === TrackingEventType.KEYPRESS) {
      // 获取通用属性和自定义参数
      let properties: TrackingProperties = {
        ...(await getCommonProperties()),
        ...trackingInfo.params,
        key: event.key,
        key_code: event.keyCode,
      }

      // 如果有自定义方法，执行它
      if (trackingInfo.customMethod) {
        properties = executeCustomMethod(trackingInfo.customMethod, properties)
      }

      // 发送到 Mixpanel
      mixpanel.track(trackingInfo.eventName, properties)

      // 开发环境下在控制台输出
      if (process.env.NODE_ENV !== 'production') {
        console.log(`[Auto-Tracking] KEYPRESS Event:`, trackingInfo.eventName, properties)
      }
    }
  },
}
