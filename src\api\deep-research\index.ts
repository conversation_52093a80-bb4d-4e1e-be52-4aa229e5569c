import { fetchWithAuth } from '@/lib/fetch'
const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface InitRequirementRequest {
  data: {
    requirement: {
      原始问题: string
      合规判定: boolean
      用户语言: string
      问题确认: string
      拒绝原因?: string
    }
    task_id: string
  }
  message: string
  success: boolean
}
// 获取输入问题的扩写
export const initRequirementApi = async (data: { question: string; user_language: string }) => {
  const response = await fetchWithAuth<Promise<InitRequirementRequest>>(
    `${apiUrl}/deep-research/init-requirement`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...data,
      }),
    },
  )

  return response
}

export const confirmRequirementApi = async (data: {
  confirmation: string
  taskId: string
  user_language: string
  question: string
}) => {
  const response = await fetchWithAuth<
    Promise<{
      message: string
      success: boolean
    }>
  >(`${apiUrl}/deep-research/confirm-requirement`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
