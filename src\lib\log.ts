import { isServer } from './environments'

export const serverLog = (msg: string, payload: unknown) => {
  // getInitialProps runs on client side in certain situations.
  // Unnecessary to log in browser because data will always be stale as the server func runs once only, unless a full reload.
  // Every client side call uses the cached _app state which means logs that you see on client side are outdated.
  // Logging getInitialProps data on browser will cause confusion during testing, best to Use AWS logs to view correct logs instead.
  if (isServer()) {
    console.log(msg, payload)
  }
}
