import { OutlineItem } from '@/store/deep-research'
import { DropPosition, DragTransferData } from './types'

/**
 * 递归收集所有项目的sequence
 */
export const collectAllSequences = (items: OutlineItem[]): string[] => {
  const sequences: string[] = []
  const traverse = (item: OutlineItem) => {
    if (item.sequence) {
      sequences.push(item.sequence)
    }
    if (item.children) {
      item.children.forEach(traverse)
    }
  }
  items.forEach(traverse)
  return sequences
}

/**
 * 根据路径获取项目
 */
export const getItemByPath = (items: OutlineItem[], path: number[]): OutlineItem | null => {
  let current = items
  let item: OutlineItem | null = null

  for (let i = 0; i < path.length; i++) {
    if (!current[path[i]]) return null
    item = current[path[i]]
    if (i < path.length - 1) {
      current = item.children || []
    }
  }

  return item
}

/**
 * 根据路径移除项目
 */
export const removeItemByPath = (items: OutlineItem[], path: number[]): OutlineItem | null => {
  if (path.length === 1) {
    return items.splice(path[0], 1)[0]
  }

  const parentPath = path.slice(0, -1)
  const parent = getItemByPath(items, parentPath)
  if (parent && parent.children) {
    return parent.children.splice(path[path.length - 1], 1)[0]
  }

  return null
}

/**
 * 在指定路径插入项目
 */
export const insertItemAtPath = (
  items: OutlineItem[],
  item: OutlineItem,
  targetPath: number[],
  position: DropPosition,
): void => {
  if (position === 'inside') {
    // 插入为子项
    const target = getItemByPath(items, targetPath)
    if (target) {
      if (!target.children) target.children = []
      target.children.push(item)
    }
  } else {
    // 插入为同级项目
    if (targetPath.length === 1) {
      // 顶级项目
      const insertIndex = position === 'before' ? targetPath[0] : targetPath[0] + 1
      items.splice(insertIndex, 0, item)
    } else {
      // 子级项目
      const parentPath = targetPath.slice(0, -1)
      const parent = getItemByPath(items, parentPath)
      if (parent && parent.children) {
        const insertIndex =
          position === 'before'
            ? targetPath[targetPath.length - 1]
            : targetPath[targetPath.length - 1] + 1
        parent.children.splice(insertIndex, 0, item)
      }
    }
  }
}

/**
 * 验证拖拽目标是否有效
 */
export const isValidDropTarget = (
  sourcePath: number[] | null,
  targetPath: number[],
  position: DropPosition,
): boolean => {
  if (!sourcePath) return false

  // 不能拖拽到自身
  if (JSON.stringify(sourcePath) === JSON.stringify(targetPath)) {
    return false
  }

  // 不能拖拽到自身的子项中
  if (targetPath.length > sourcePath.length) {
    const isChild = sourcePath.every((val, index) => val === targetPath[index])
    if (isChild) return false
  }

  return true
}

/**
 * 递归处理大纲数据，重新计算序号
 */
export const processOutlineDataRecursive = (
  items: OutlineItem[],
  level = 0,
  parentSequence = '',
): OutlineItem[] => {
  return items.map((item, index) => {
    const currentSequence = parentSequence ? `${parentSequence}.${index + 1}` : `${index + 1}`
    return {
      ...item,
      level,
      sequence: currentSequence,
      children: item.children
        ? processOutlineDataRecursive(item.children, level + 1, currentSequence)
        : undefined,
    }
  })
}

/**
 * 嵌套重排序逻辑
 */
export const reorderNestedOutlineItems = (
  items: OutlineItem[],
  sourcePath: number[],
  targetPath: number[],
  position: DropPosition,
): OutlineItem[] => {
  const itemsCopy = JSON.parse(JSON.stringify(items)) // 深拷贝

  // 移除源项目
  const draggedItem = removeItemByPath(itemsCopy, sourcePath)
  if (!draggedItem) return items

  // 调整目标路径（如果源项目在目标项目之前，需要调整索引）
  const adjustedTargetPath = [...targetPath]
  if (sourcePath.length === targetPath.length) {
    // 同级移动
    const lastSourceIndex = sourcePath[sourcePath.length - 1]
    const lastTargetIndex = targetPath[targetPath.length - 1]

    if (lastSourceIndex < lastTargetIndex) {
      adjustedTargetPath[adjustedTargetPath.length - 1] = lastTargetIndex - 1
    }
  }

  // 插入到目标位置
  insertItemAtPath(itemsCopy, draggedItem, adjustedTargetPath, position)

  // 重新处理数据（重新计算序号等）
  return processOutlineDataRecursive(itemsCopy)
}

/**
 * 解析拖拽传输数据
 */
export const parseDragTransferData = (dataTransfer: DataTransfer): DragTransferData | null => {
  try {
    const data = dataTransfer.getData('text/plain')
    return JSON.parse(data) as DragTransferData
  } catch (error) {
    console.error('拖拽数据解析失败:', error)
    return null
  }
}

/**
 * 创建拖拽传输数据
 */
export const createDragTransferData = (item: OutlineItem, sourcePath: number[]): string => {
  const data: DragTransferData = {
    itemId: item.id!,
    sourcePath,
  }
  return JSON.stringify(data)
}
