import { ReactNode } from 'react'
import { Text, TextEnum } from '../../text'
import clsx from 'clsx'
import useIsMobileScreen from '@/components/ui/useIsMobile'

interface IAccountTitleProps {
  title: ReactNode
  desc: ReactNode
}

export const AccountTitle = (props: IAccountTitleProps) => {
  const [isMobile] = useIsMobileScreen()

  return (
    <div
      className={(clsx('w-full text-center'), isMobile ? 'mb-0 mt-10' : 'mb-20')}
      data-component='AccountTitle'>
      <Text type={TextEnum.H2} className={clsx('text-white', isMobile ? 'mt-28 text-center' : '')}>
        {props.title}
      </Text>
      <Text type={TextEnum.Body_big} className={clsx('text-white', isMobile ? 'text-center' : '')}>
        {props.desc}
      </Text>
    </div>
  )
}
