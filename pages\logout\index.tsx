import { GetServerSideProps } from 'next'
import { smartiesRoutes } from '@/routers'
import { clearCookies } from '@/lib/auth'
import { serverLog } from '@/lib/log'

const Logout = () => <></>

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const searchParams = new URLSearchParams(ctx.query as any)
  try {
    clearCookies(ctx)
  } catch (error) {
    serverLog('clear cookies error', error)
  }
  return {
    redirect: {
      destination: `${smartiesRoutes.login}?${searchParams}`,
      permanent: false,
    },
    props: {},
  }
}

export default Logout
