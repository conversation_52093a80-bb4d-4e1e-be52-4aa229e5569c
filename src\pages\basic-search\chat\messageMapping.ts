import { MessageRoleEnum, SingleMessageType, UserMessage } from '@/api/getSession'
import { MessageType, PoTypeEnum } from '@/types'
import { cloneDeep } from 'lodash'

const initMessage = {
  [PoTypeEnum.MARKET]: [
    {
      value: '',
      key: 'description',
    },
    {
      value: '',
      key: 'targetMarket',
    },
  ],
  [PoTypeEnum.COMPANY]: [
    {
      value: '',
      key: 'companyName',
    },
  ],
  [PoTypeEnum.GENERAL]: [
    {
      value: '',
      key: 'query',
    },
  ],
  [PoTypeEnum.REGULATION]: [
    {
      value: '',
      key: 'description',
    },
    {
      value: '',
      key: 'targetMarket',
    },
    {
      value: '',
      key: 'currentLocation',
    },
  ],
  [PoTypeEnum.RISK]: [
    {
      value: '',
      key: 'description',
    },
  ],
  [PoTypeEnum.SWOT]: [
    {
      value: '',
      key: 'description',
    },
  ],
  [PoTypeEnum.TOPIC]: [
    {
      value: '',
      key: 'topic',
    },
    {
      value: '',
      key: 'details',
    },
  ],
}

const processUserMessageContent = (messages: UserMessage, type: PoTypeEnum): UserMessage => {
  const content = cloneDeep(messages.content)
  if (content.length > 0 && content[0].key === 'query' && type !== PoTypeEnum.GENERAL) {
    const mappedContent = initMessage[type.toLocaleUpperCase() as PoTypeEnum]
    mappedContent[0].value = content[0].value
    return {
      ...messages,
      content: mappedContent as MessageType,
    }
  }
  return messages
}

export const messagesMap = (
  type: PoTypeEnum,
  messages: SingleMessageType[],
): SingleMessageType[] => {
  const mappedMessage = messages.map((message, idx) => {
    if ((message as UserMessage).role === MessageRoleEnum.USER && idx === 0) {
      const userMessage = message as UserMessage
      return processUserMessageContent(userMessage, type)
    } else {
      return message
    }
  })
  return mappedMessage
}
