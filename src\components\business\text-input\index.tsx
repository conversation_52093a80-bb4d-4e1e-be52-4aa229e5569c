import { Text, TextEnum } from '@/components/business/text'
import { Namespace } from '@/i18n'
import { cn } from '@/lib/utils'
import { PaperAirplaneIcon } from '@heroicons/react/24/outline'
import { StopCircleIcon } from '@heroicons/react/24/solid'
import clsx from 'clsx'
import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'

export interface TextinputProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  withIcon?: boolean
  disabled?: boolean
  showStop?: boolean
  isStopEnterPrevent?: boolean
  maxHeight?: number
  onStop?: () => void
  onSearch?: (key: string) => void
  onEnter?: (value: string) => void
}

const Textinput = React.forwardRef<HTMLTextAreaElement, TextinputProps>(
  (
    {
      withIcon,
      className,
      onEnter,
      disabled,
      showStop,
      onStop,
      onSearch,
      value,
      onChange,
      maxHeight = 144,
      isStopEnterPrevent = true,
      ...props
    },
    ref,
  ) => {
    const { t } = useTranslation(Namespace.BASIC_SEARCH)
    const textareaRef = React.useRef<HTMLTextAreaElement | null>(null)
    const [isComposing, setIsComposing] = React.useState(false)
    const [extended, setExtened] = useState(false)

    React.useEffect(() => {
      adjustHeight()
    }, [value])

    // 动态调整 textarea 高度
    const adjustHeight = () => {
      const textarea = textareaRef.current

      if (textarea) {
        textarea.style.height = 'auto' // 先重置高度
        // const maxHeight = maxHeight // 设置最大高度
        const newHeight = textarea.scrollHeight // 获取内容的实际高度

        setExtened(newHeight > 44) // 控制右侧 icon space

        if (newHeight > maxHeight) {
          textarea.style.height = `${maxHeight}px` // 超过最大高度，设置为 330px
          textarea.style.overflowY = 'auto' // 显示滚动条
          textarea.style.resize = 'none' // 禁止 resize
        } else {
          textarea.style.height = `${newHeight}px` // 根据内容动态设置高度
          textarea.style.overflowY = 'hidden' // 隐藏滚动条
          textarea.style.resize = 'none' // 禁止 resize
        }
      }
    }

    // 处理按键事件
    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (isStopEnterPrevent) {
        if (e.key === 'Enter') {
          if (e.shiftKey) {
            adjustHeight()
          } else if (!isComposing) {
            e.preventDefault() // 阻止默认换行行为
            if (onEnter && !showStop && !disabled) {
              onEnter(textareaRef.current?.value || '')
            }
          }
        } else {
          adjustHeight()
        }
      } else {
        adjustHeight()
      }
    }

    // 处理输入法状态
    const handleCompositionStart = () => {
      setIsComposing(true)
    }

    // compositionend 表示中文字符输入结束
    const handleCompositionEnd = () => {
      setIsComposing(false)
      adjustHeight()
    }

    // 处理输入事件（包括删除）
    const handleInput = () => {
      adjustHeight()
    }

    const handleIconClick = () => {
      if (showStop && onStop) {
        onStop()
      }
      if (!showStop && !disabled && onSearch) {
        onSearch(textareaRef?.current?.value ?? '')
      }
    }

    React.useEffect(() => {
      adjustHeight()
    }, [])

    return (
      <div className={clsx('relative flex items-center', className)}>
        <textarea
          className={cn(
            'flex min-h-[46px] w-full rounded-md border border-input bg-card px-2 py-3 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
            withIcon ? 'pr-14' : '',
            className,
          )}
          ref={(el) => {
            textareaRef.current = el
            if (typeof ref === 'function') {
              ref(el)
            } else if (ref) {
              ;(ref as React.MutableRefObject<HTMLTextAreaElement | null>).current = el
            }
          }}
          rows={1} // 默认显示一行
          onKeyDown={handleKeyDown}
          onInput={handleInput} // 处理删除和输入
          onCompositionStart={handleCompositionStart} // 中文输入开始
          onCompositionEnd={handleCompositionEnd} // 中文输入结束
          value={value}
          onChange={onChange}
          {...props}
        />
        {withIcon && (
          <div
            className={clsx(
              'absolute right-2 top-2 flex h-[30px] transform items-center justify-center rounded-sm bg-primary px-[7px] text-white',
              disabled ? 'bg-primary-disabled' : 'cursor-pointer',
              extended ? 'bottom-1' : '',
            )}
            onClick={handleIconClick}>
            {showStop ? (
              <>
                <StopCircleIcon className='mr-1 h-4 w-4' />
                <Text type={TextEnum.Body_medium}> {t('common.stop')}</Text>
              </>
            ) : (
              <PaperAirplaneIcon className='h-4 w-4' />
            )}
          </div>
        )}
      </div>
    )
  },
)
Textinput.displayName = 'Textinput'

export { Textinput }
