import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import {
  reportCreate<PERSON><PERSON>,
  deleteReport<PERSON><PERSON>,
  getReport<PERSON>pi,
  ReportItemType,
  GeneratorStatus,
} from '@/api/document/index'

interface ReportStore {
  currentReport: ReportItemType
  isLoadingDocument: boolean
  getReportById: (reportId: string) => Promise<ReportItemType>
  updateCurrentReport: (res: ReportItemType) => void
  createReport: () => Promise<{ reportId: string }>
  deleteReportById: (reportId: string) => Promise<void>
}
export const useReport = create<ReportStore>()(
  immer((set, get) => ({
    currentReport: {},
    // 更新文档生成页
    isLoadingDocument: true,
    // 获取文档详情
    getReportById: async (reportId) => {
      set({ isLoadingDocument: true })
      try {
        const res = await getReportApi({ reportId })
        // 初始状态下 documentGeneratorStatus 为 INIT
        if (res.content) {
          res.content =
            res.content +
            `
            
:::component${JSON.stringify(res.refs)}:::`
        }
        let status = res.status as GeneratorStatus
        if (res.status === GeneratorStatus.CANCELED || res.status === GeneratorStatus.FAILED) {
          status = GeneratorStatus.INIT
        } else {
          status = res.status
        }
        const data = {
          ...res,
          documentGeneratorStatus: status,
        }
        get().updateCurrentReport(data)
        set({ isLoadingDocument: false })
        return data
      } catch (error) {
        console.error('获取报告详情失败', error)
        set({ isLoadingDocument: false })
      }
    },
    // 更新当前的文档对象
    updateCurrentReport: (res) => {
      set({
        currentReport: {
          ...get().currentReport,
          ...res,
        },
      })
    },
    // 创建文档
    createReport: async () => {
      try {
        const res = await reportCreateApi()
        return res
      } catch (error) {
        console.error('创建报告失败', error)
      }
    },
    // 删除文档
    deleteReportById: async (reportId) => {
      try {
        await deleteReportApi({ reportId })
      } catch (error) {
        console.error('删除报告失败', error)
      }
    },
  })),
)
