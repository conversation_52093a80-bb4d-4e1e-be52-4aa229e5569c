import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/router'
import { AccountLayout } from '@/components/business/accounts/layout'
import { AccountTitle } from '@/components/business/accounts/title'
import { ResendEmail } from '@/components/business/accounts/resend'
import { Text, TextEnum } from '@/components/business/text'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Namespace } from '@/i18n'
import { smartiesRoutes } from '@/routers'
import useLanguage from '@/i18n/useLanguage'
import { SupportedLangs } from '@/i18n'
import { forgotPassword, ForgotPasswordResponse } from '@/api/forgotPassword'
import { toast } from 'sonner'
import { setCookie, smartiesDomain } from '@/lib/cookie'
import { resetPassword, ResetPasswordResponse } from '@/api/resetPassword'
import { AuthError } from '@/api/types'
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'
import clsx from 'clsx'
import useIsMobileScreen from '@/components/ui/useIsMobile'

type PageStatus = 'init' | 'check' | 'verify'

const PasswordReset = () => {
  const { t } = useTranslation([Namespace.LOGIN])
  const router = useRouter()
  const { query } = router
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState<string>('')
  const [confirmPassword, setConfirmPassword] = useState<string>('')
  const { language } = useLanguage({ loggedIn: false })
  const isVerifyCode = query.code && query.action === 'verify'
  const [pageStatus, setPageStatus] = useState<PageStatus>(isVerifyCode ? 'verify' : 'init')
  const [buttonDisabled, setButtonDisabled] = useState(false)

  const [passwordVisible, setPasswordVisible] = useState(false)
  const [passConfirmWordVisible, setPassConfirmWordVisible] = useState(false)

  const bottom = (
    <Text
      type={TextEnum.Body_medium}
      className={'mt-10 w-[420px] text-center !leading-6 text-white'}>
      {t('login:reset.status')}
      <span
        className='cursor-pointer text-primary'
        onClick={() => router.push({ pathname: smartiesRoutes.login, query })}>
        &nbsp; {t('login:reset.action')} &nbsp;
      </span>
    </Text>
  )

  const [isMobile] = useIsMobileScreen()

  return (
    <AccountLayout isIframe={!!query?.lang && !!query?.redirectUrl}>
      {pageStatus === 'init' ? (
        <>
          <div
            className={clsx('flex-center flex-col', isMobile ? 'w-11/12' : 'w-[420px]')}
            data-component='EmailLogin'>
            <AccountTitle title={t('login:reset.title')} desc={t('login:reset.desc')} />
            <div className='flex w-full flex-col gap-2.5'>
              <div className={clsx('text-sm font-normal text-white', isMobile ? 'mt-10' : '')}>
                {t('login:input.email')}
              </div>
              <Input
                value={email}
                placeholder={t('login:input.emailPlaceholder')}
                className='bg-transparent text-white'
                onChange={(event) => setEmail(event.target.value)}
              />
            </div>
            <Button
              className='mt-[120px] w-full cursor-pointer rounded-sm bg-primary hover:bg-primary disabled:bg-primary-disabled disabled:opacity-100'
              disabled={!email || buttonDisabled}
              onClick={() => {
                setButtonDisabled(true)
                forgotPassword({ email, language: language as SupportedLangs })
                  .then(() => {
                    setPageStatus('check')
                  })
                  .finally(() => setButtonDisabled(false))
              }}>
              {t('login:login.button')}
            </Button>
          </div>
          {bottom}
        </>
      ) : null}
      {pageStatus === 'check' ? (
        <ResendEmail
          suffix={bottom}
          email={email}
          disabled={buttonDisabled}
          onResend={() => {
            setButtonDisabled(true)
            forgotPassword({ email, language: language as SupportedLangs })
              .then((res) => {
                if ((res as ForgotPasswordResponse).result) {
                  toast(t('login:toast.resend'), {
                    duration: 3000,
                  })
                }
              })
              .finally(() => setButtonDisabled(false))
          }}
        />
      ) : null}
      {pageStatus === 'verify' ? (
        <>
          <AccountTitle title={t('login:change.title')} desc={t('login:change.desc')} />
          <div className='flex-center w-full flex-col'>
            <div className='flex w-full flex-col gap-2.5'>
              <div className='text-sm font-normal text-white'>
                {t('login:change.passwordLabel')}
              </div>
              <div className='relative'>
                <Input
                  value={password}
                  type={passwordVisible ? 'text' : 'password'}
                  placeholder={t('login:change.passwordPlaceholder')}
                  className='bg-transparent text-white'
                  onChange={(event) => setPassword(event.target.value)}
                />
                {passwordVisible ? (
                  <EyeIcon
                    className='absolute bottom-2.5 right-[20px] cursor-pointer text-white'
                    width={20}
                    height={20}
                    onClick={() => setPasswordVisible(false)}
                  />
                ) : (
                  <EyeSlashIcon
                    width={20}
                    height={20}
                    className='absolute bottom-2.5 right-[20px] cursor-pointer text-white'
                    onClick={() => setPasswordVisible(true)}
                  />
                )}
              </div>
            </div>
            <div className='mb-5 mt-[15px] flex w-full flex-col gap-2.5'>
              <div className='text-sm font-normal text-white'>
                {t('login:change.confirmPasswordLabel')}
              </div>
              <div className='relative'>
                <Input
                  value={confirmPassword}
                  type={passConfirmWordVisible ? 'text' : 'password'}
                  placeholder={t('login:change.confirmPasswordPlaceholder')}
                  className='relative bg-transparent pr-[60px] text-white'
                  onChange={(event) => setConfirmPassword(event.target.value)}
                />
                {passConfirmWordVisible ? (
                  <EyeIcon
                    className='absolute bottom-2.5 right-[20px] cursor-pointer text-white'
                    width={20}
                    height={20}
                    onClick={() => setPassConfirmWordVisible(false)}
                  />
                ) : (
                  <EyeSlashIcon
                    width={20}
                    height={20}
                    className='absolute bottom-2.5 right-[20px] cursor-pointer text-white'
                    onClick={() => setPassConfirmWordVisible(true)}
                  />
                )}
              </div>
            </div>
            <Button
              className='mb-5 mt-[120px] w-full cursor-pointer rounded-sm bg-primary hover:bg-primary disabled:bg-primary-disabled disabled:opacity-100'
              disabled={!(confirmPassword && password) || buttonDisabled}
              onClick={() => {
                setButtonDisabled(true)
                resetPassword({
                  code: query.code as string,
                  newPassword: password!,
                })
                  .then((res) => {
                    if ((res as AuthError).errCode) {
                      const response = res as AuthError
                      toast(response.errMsg, {
                        duration: 3000,
                      })
                    } else {
                      const response = res as ResetPasswordResponse
                      setCookie('smartToken', response.accessToken, {
                        maxAge: 7 * 24 * 60 * 60 * 1000,
                        httpOnly: false,
                        domain: smartiesDomain,
                      })
                      toast(t('login:toast.change'), {
                        duration: 3000,
                      })
                      router.push(smartiesRoutes.basicSearch.home)
                    }
                  })
                  .finally(() => setButtonDisabled(false))
              }}>
              {t('login:login.button')}
            </Button>
            <Text
              type={TextEnum.Body_medium}
              className={'w-[420px] text-center !leading-6 text-white'}>
              {t('login:account.withoutAccountStatus')}
              <span
                className='cursor-pointer text-primary'
                onClick={() => router.push({ pathname: smartiesRoutes.signup, query })}>
                &nbsp; {t('login:account.withoutAccountAction')} &nbsp;
              </span>
            </Text>
          </div>
        </>
      ) : null}
    </AccountLayout>
  )
}

export default PasswordReset
