import { useEffect, useState, useRef, useCallback } from 'react'

export const useCloseOutside = () => {
  const dropdownRef = useRef<HTMLDivElement>(null)

  const [isOpen, setIsOpen] = useState<boolean>(false)

  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
      const selection = window.getSelection()
      selection?.removeAllRanges()
      setIsOpen(false)
    }
  }, [])

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])
  return {
    dropdownRef,
    isOpen,
    setIsOpen,
  }
}
