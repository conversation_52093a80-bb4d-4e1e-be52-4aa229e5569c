import { Html, Head, Main, NextScript } from 'next/document'

export default function Document() {
  return (
    <Html lang='en'>
      <Head>
        <meta charSet='utf-8' />
        <meta
          name='description'
          content='Your AI Assistant for Real-Time Market Research, Competitor Analysis, SWOT, and Compliance Insights.'
        />
        <meta
          name='keywords'
          content='Al-powered answer engine, AI copilot for small business, AI assistant, market research tools, ai market research, small business using ai, business writing, business plan software, free swot analysis software, export compliance AI tool, free risk assessment software'
        />
        <meta name='google' content='notranslate' />
        <script src='https://d1ij1j35k83uw7.cloudfront.net/mixpanel/mixpanel.js' defer />
        <script src='https://accounts.google.com/gsi/client' async />
        {/* <link
          rel='preload'
          href='https://d1ij1j35k83uw7.cloudfront.net/player/player.js'
          as='script'
        /> */}
        <link
          rel='preload'
          href='https://d1ij1j35k83uw7.cloudfront.net/pdfjs/3.11.174/pdf.worker.min.js'
          as='script'
        />

        {/* <link rel='stylesheet' href='https://at.alicdn.com/t/c/font_4801925_8hptmglrc39.css' /> */}
        <script src='https://at.alicdn.com/t/c/font_4801925_8hptmglrc39.js' async />
      </Head>
      <body className='overscroll-y-none'>
        <Main />
        <NextScript />
        <div id='modal' />
      </body>
    </Html>
  )
}
