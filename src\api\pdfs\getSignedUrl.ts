import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL
export interface GetSignedUrlRequest {
  fileName: string
}

export type GetSignedUrlResponse = {
  url: string
  fields: {
    key: string
    ACL: string
    'Content-Type': 'application/pdf; charset=utf-8'
    'x-amz-meta-userId': string
    bucket: string
    'X-Amz-Algorithm': string
    'X-Amz-Credential': string
    'X-Amz-Date': string
    'X-Amz-Security-Token': string
    Policy: string
    'X-Amz-Signature': string
  }
}

export const getSignedUrl = async (data: GetSignedUrlRequest): Promise<GetSignedUrlResponse> => {
  const response = await fetchWithAuth<GetSignedUrlResponse>(`${apiUrl}/getUploadUrl`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
