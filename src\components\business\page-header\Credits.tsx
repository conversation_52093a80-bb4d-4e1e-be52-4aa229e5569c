import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import clsx from 'clsx'
import { Namespace } from '@/i18n'
import { Text, TextEnum } from '../text'
import { useUserStore } from '@/store/userStore'
import { SubscriptionEnum } from '@/types'
import { smartiesRoutes } from '@/routers'
import { useRouter } from 'next/router'
import { TooltipArrow, TooltipProvider } from '@radix-ui/react-tooltip'
import { Tooltip, TooltipContent, TooltipTrigger, TooltipPortal } from '@/components/ui/tooltip'

export const Credits = () => {
  const router = useRouter()
  const { t } = useTranslation([Namespace.GLOBAL])

  const user = useUserStore((state) => state.user)

  const [credits, seCredits] = useState<number | undefined>(undefined)
  const [showCredits, setShowCredits] = useState<boolean>(false)

  useEffect(() => {
    if (
      user?.currentPlan === SubscriptionEnum.FREE ||
      user?.currentPlan === SubscriptionEnum.BASIC_MONTH ||
      user?.currentPlan === SubscriptionEnum.BASIC_YEAR
    ) {
      seCredits(user.usage.totalQuota - user.usage.totalUsage)
      setShowCredits(true)
    } else {
      setShowCredits(false)
    }
  }, [user?.currentPlan, user?.usage])

  const handleGoPricingPage = () => {
    if (router.pathname !== smartiesRoutes.pricing) {
      router.push(smartiesRoutes.pricing)
    }
  }

  const handleGoSubscriptionPage = () => {
    if (router.pathname !== smartiesRoutes.subscription) {
      router.push(smartiesRoutes.subscription)
    }
  }

  return (
    <>
      {showCredits ? (
        <div
          className={clsx(
            'flex h-8 shrink-0 items-center justify-between gap-4 rounded-sm bg-secondary pl-2 pr-1',
            user?.currentPlan !== SubscriptionEnum.FREE ? '!pr-2' : '',
          )}>
          <TooltipProvider delayDuration={0}>
            <Tooltip>
              <TooltipTrigger
                data-state='instant-open'
                className='flex-1 overflow-hidden text-ellipsis whitespace-nowrap rounded-sm text-left group-hover:bg-primary-hover'>
                {credits !== undefined && (
                  <div className='flex-v-center cursor-pointer' onClick={handleGoSubscriptionPage}>
                    <Text type={TextEnum.Body_medium} className='text-secondary-black-1'>
                      {credits}
                    </Text>
                    <Text type={TextEnum.Body_medium} className='ml-1 text-secondary-black-3'>
                      {t('setting.usage.credits')}
                    </Text>
                  </div>
                )}
              </TooltipTrigger>
              <TooltipPortal>
                <TooltipContent
                  side='bottom'
                  sideOffset={15}
                  value={true}
                  className='z-[1001] rounded-sm border-none bg-tooltip p-2'>
                  <TooltipArrow />
                  <div className='text-left text-tooltip-foreground'>
                    {user?.usage && (
                      <Text type={TextEnum.Body_medium}>
                        {t('setting.usage.freeCredits', {
                          count: user?.usage.freeQuota - user?.usage.freeUsage,
                        })}
                      </Text>
                    )}
                    {user?.usage && (
                      <Text type={TextEnum.Body_medium}>
                        {t('setting.usage.paidCredits', {
                          count: user?.usage.paidQuota - user?.usage.paidUsage,
                        })}
                      </Text>
                    )}
                  </div>
                </TooltipContent>
              </TooltipPortal>
            </Tooltip>
          </TooltipProvider>

          {user?.currentPlan === SubscriptionEnum.FREE && (
            <div
              onClick={handleGoPricingPage}
              className={clsx(
                'flex-center ai-smarties-color-bg h-6 shrink-0 cursor-pointer rounded px-1.5 italic text-primary-foreground',
              )}>
              <Text type={TextEnum.H7}>{t('setting.usage.upgrade')}</Text>
            </div>
          )}
        </div>
      ) : null}
    </>
  )
}
