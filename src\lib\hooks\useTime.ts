import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
export const useTime = () => {
  const { t } = useTranslation(Namespace.GLOBAL)
  const formatTimestamp = (timestamp) => {
    const now = new Date()
    const date = new Date(timestamp)

    // 获取今天和昨天的开始时间
    const startOfToday = new Date(now.setHours(0, 0, 0, 0))
    const startOfYesterday = new Date(startOfToday)
    startOfYesterday.setDate(startOfYesterday.getDate() - 1)

    // 获取年、月、日、小时、分钟
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，所以加1
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')

    // 判断是今天、昨天，还是普通日期
    if (date >= startOfToday) {
      // 显示今天的时间
      return `${t('today')} ${hour}:${minute}`
    } else if (date >= startOfYesterday) {
      // 显示昨天的时间
      return `${t('yesterday')} ${hour}:${minute}`
    } else {
      // 显示其他日期
      return `${year}/${month}/${day} ${hour}:${minute}`
    }
  }
  return formatTimestamp
}
