import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface RefreshPlanResponse {
  subscriptionId?: string
}

export const refreshPlan = async (): Promise<RefreshPlanResponse> => {
  const response = await fetchWithAuth<RefreshPlanResponse>(`${apiUrl}/subscription/refreshPlan`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  })

  return response
}
