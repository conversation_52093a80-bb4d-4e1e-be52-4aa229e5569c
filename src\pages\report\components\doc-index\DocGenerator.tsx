import { GeneratorStatus } from '@/api/document/index'
import { getUser } from '@/api/getUser'
import LoadingDots from '@/components/business/loading-dots'
import MarkdownParser from '@/components/business/markdown-parser'
import { Text, TextEnum } from '@/components/business/text'
import { Namespace } from '@/i18n'
import { useUserStore } from '@/store/userStore'
import { ArrowPathIcon } from '@heroicons/react/24/outline'
import { useEffect, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'sonner'
import useWebSocketWithReconnection from '../../../deep-explore/socket'
import { useGenerate } from '../../store/useGenerate'
import { useReport } from '../../store/useReport'
export const DocGenerator = () => {
  const { t } = useTranslation([Namespace.GENERATOR, Namespace.GLOBAL])
  const currentReport = useReport((s) => s.currentReport)
  const updateCurrentReport = useReport((s) => s.updateCurrentReport)
  const isCancelled = useGenerate((s) => s.isCancelled)
  const setReportIdMap = useGenerate((s) => s.setReportIdMap)
  const reportIdMap = useGenerate((s) => s.reportIdMap)
  const reportProcessId = useGenerate((s) => s.reportProcessId)
  const updateUser = useUserStore((state) => state.updateUser)
  const generaReportRef = useRef(currentReport.content || '')
  const reportProcessIdRef = useRef(reportProcessId)

  // 创建连接
  const handleSocketMessage = (message) => {
    const { type, reportId, data } = message
    if (
      type === 'report' &&
      reportId === currentReport.reportId &&
      data.reportProcessId === reportProcessIdRef.current
    ) {
      if (data.status === 'OUTPUTING') {
        generaReportRef.current += data.message
        setReportIdMap(reportId, generaReportRef.current)
        updateCurrentReport({
          documentGeneratorStatus: GeneratorStatus.CREATING,
        })
      } else if (data.status === 'FINISH') {
        updateCurrentReport({
          content: generaReportRef.current,
          documentGeneratorStatus: GeneratorStatus.FINISH,
        })
        reportProcessIdRef.current = ''
      }
    }
    if (
      type === 'report' &&
      reportId === currentReport.reportId &&
      data.status === GeneratorStatus.FAILED
    ) {
      generaReportRef.current = t('global:error.systemError')
      reportProcessIdRef.current = ''
      updateCurrentReport({
        documentGeneratorStatus: GeneratorStatus.FAILED,
      })
      updateCredits()
    }
  }
  const updateCredits = async () => {
    const user = await getUser()
    if (user) updateUser(user)
    return user
  }
  const onRefresh = () => {
    if (currentReport.documentGeneratorStatus === GeneratorStatus.CREATING) {
      toast(
        <div
          onClick={() => {
            window.location.reload()
          }}
          className='flex items-center'>
          <ArrowPathIcon className='mr-2 h-5 w-5 cursor-pointer'></ArrowPathIcon>
          {t('global:error.netError')}
        </div>,
        {
          duration: 3000,
        },
      )
    }
  }
  const { socket, isConnected, retryFailed, startWebSocket, closeWebSocket } =
    useWebSocketWithReconnection({
      onMessage: handleSocketMessage,
      onRetryRefresh: onRefresh,
    })

  useEffect(() => {
    if (currentReport.documentGeneratorStatus === GeneratorStatus.WAITING) {
      if (!socket || !isConnected) {
        startWebSocket()
      }
    }
    if (currentReport.documentGeneratorStatus === GeneratorStatus.FINISH && socket && isConnected) {
      closeWebSocket()
      reportProcessIdRef.current = ''
    }
  }, [currentReport.documentGeneratorStatus, socket, isConnected])
  useEffect(() => {
    // 刷新页面时，重置reportProcessId
    reportProcessIdRef.current = ''
    const handleOffline = () => {
      onRefresh()
    }
    window.addEventListener('offline', handleOffline)
    return () => {
      window.removeEventListener('offline', handleOffline)
    }
  }, [])
  useEffect(() => {
    if (retryFailed) {
      onRefresh()
    }
  }, [retryFailed])

  useEffect(() => {
    if (isCancelled) {
      generaReportRef.current = ''
      reportProcessIdRef.current = ''
      setReportIdMap(currentReport.reportId, '')
    }
  }, [isCancelled])

  useEffect(() => {
    reportProcessIdRef.current = reportProcessId
  }, [reportProcessId])

  useEffect(() => {
    // 读取本地缓存
    if (reportIdMap[currentReport.reportId]) {
      updateCurrentReport({
        documentGeneratorStatus: currentReport.documentGeneratorStatus,
      })
      generaReportRef.current = reportIdMap[currentReport.reportId]
    }
  }, [reportIdMap])

  return (
    <div className='mt-8 pb-40'>
      {/* 文档初始状态 */}
      {(currentReport.documentGeneratorStatus === GeneratorStatus.INIT || isCancelled) && (
        <Text type={TextEnum.Body_medium} className='mt-7 text-muted-foreground'>
          {t('generator:docDesc')}
        </Text>
      )}
      {/* 文档等待生成 */}
      {currentReport.documentGeneratorStatus === GeneratorStatus.WAITING && (
        <div className='flex items-center gap-2'>
          <LoadingDots size={4} />
          <Text type={TextEnum.Body_medium}> {t('generator:documentGenerating')} </Text>
        </div>
      )}
      {/* 文档生成中 */}
      {!isCancelled &&
        (currentReport.documentGeneratorStatus === GeneratorStatus.CREATING ||
          currentReport.documentGeneratorStatus === GeneratorStatus.FINISH ||
          currentReport.documentGeneratorStatus === GeneratorStatus.FAILED) && (
          <div>
            <MarkdownParser content={generaReportRef.current} />
            {/* <ReferenceList referenceList={currentReport.refs} /> */}
          </div>
        )}
    </div>
  )
}
