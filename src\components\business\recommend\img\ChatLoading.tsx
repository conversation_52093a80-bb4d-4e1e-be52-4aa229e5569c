import { Skeleton } from '@/components/ui/skeleton'

const ChatSkeleton = () => {
  return (
    <div className='flex space-x-4'>
      <Skeleton className='h-12 w-12 shrink-0 rounded-full bg-white' />
      <div className='space-y-2'>
        <Skeleton className='h-4 w-[300px] bg-white' />
        <Skeleton className='h-4 w-[250px] bg-white' />
        <Skeleton className='h-4 w-[200px] bg-white' />
      </div>
    </div>
  )
}

const ChatLoading = () => {
  return (
    <>
      {new Array(5).fill('').map((_, idx) => (
        <ChatSkeleton key={idx} />
      ))}
    </>
  )
}

export default ChatLoading
