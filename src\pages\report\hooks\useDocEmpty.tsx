import { Cog6ToothIcon } from '@heroicons/react/24/outline'
import { LightBulbIcon, ChartPieIcon, Bars3BottomLeftIcon } from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'

// bars-3-bottom-left

export enum ReportTypeEnum {
  EMPTY = 'EMPTY',
  BUSINESS_PLAN = 'BUSINESS_PLAN',
  COMPANY = 'COMPANY',
}
export interface docListProps {
  key: string
  name: string
}

export const useDocEmpty = () => {
  const { t } = useTranslation(Namespace.GENERATOR)
  const docList: docListProps[] = [
    {
      name: t('emptyDocument'),
      key: 'empty',
      type: ReportTypeEnum.EMPTY,
      render: () => {
        return <Bars3BottomLeftIcon className='mr-4 h-5 w-5 rounded bg-primary px-0.5 text-white' />
      },
    },
    {
      name: t('businessPlan'),
      key: 'plan',
      type: ReportTypeEnum.BUSINESS_PLAN,
      render: () => {
        return <LightBulbIcon className='mr-4 h-5 w-5 rounded bg-[#2ED3B7] p-[2px] text-white' />
      },
    },
    {
      name: t('companyAnalysis'),
      key: 'analysis',
      type: ReportTypeEnum.COMPANY,
      render: () => {
        return <ChartPieIcon className='mr-4 h-5 w-5 rounded bg-[#22CCEE] p-[2px] text-white' />
      },
    },
    {
      name: t('setting'),
      key: 'set',
      render: () => {
        return <Cog6ToothIcon className='mr-4 h-5 w-5' />
      },
    },
  ]

  return docList
}
