import { Skeleton } from '@/components/ui/skeleton'
// import RecommandSkeletonItem from '@/components/business/recommend/skeleton/Skeleton'
import { PaperAirplaneIcon } from '@heroicons/react/24/outline'

const PageSkeleton = () => {
  return (
    <>
      <div className='flex-h-center hide-scrollbar flex-1 gap-2 overflow-auto px-14 pt-8'>
        <div className='ml-16 min-w-[600px] max-w-[760px] shrink-0'>
          <div className='flex space-x-4'>
            <Skeleton className='h-8 w-8 shrink-0 rounded-full bg-skeleton' />
            <div className='mt-1'>
              <Skeleton className='h-6 w-[248spx] bg-skeleton' />
              <Skeleton className='mt-4 h-5 w-[660px] bg-skeleton' />
              <Skeleton className='mt-2.5 h-5 w-[660px] bg-skeleton' />
              <Skeleton className='mt-2.5 h-5 w-[334px] bg-skeleton' />
            </div>
          </div>
        </div>
        <div className={'mt-1 w-80'}>
          <div className='flex flex-col gap-2'>{/* <RecommandSkeletonItem count={2} /> */}</div>
        </div>
      </div>
      <div className='flex-center py-2.5'>
        <div className='fle-col flex min-w-[720px] flex-col items-center'>
          <div className={'relative flex w-full justify-end rounded-sm bg-skeleton p-1'}>
            <div
              className={
                'flex h-10 transform items-center justify-center rounded-sm bg-primary-disabled px-3 text-white'
              }>
              <PaperAirplaneIcon className={'h-4 w-4'} />
            </div>
          </div>
          <div className='mt-2.5 h-4 w-[334px] rounded bg-skeleton' />
        </div>
        <div className='w-[260px] shrink-0'></div>
      </div>
    </>
  )
}

export default PageSkeleton
