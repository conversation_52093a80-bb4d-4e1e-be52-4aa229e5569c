import Image from 'next/image'
import { Text, TextEnum } from '@/components/business/text'
import { TextHtml } from '../TextHtml'
export const Pdf = ({ item }) => {
  return (
    <div className='mt-3 rounded-sm bg-primary-bg px-2 py-3'>
      <div className='flex-v-center gap-2'>
        <Image src={'/images/pdf_icon.svg'} alt='pdf icon' width={32} height={32} />
        <div className='flex-1 overflow-hidden'>
          <Text type={TextEnum.H5} className='line-clamp-1'>
            {item.title || item.fileName}
          </Text>
          {item.snippet && (
            <Text
              type={TextEnum.Body_small}
              className='h-8 overflow-hidden text-ellipsis break-words'>
              {item.snippet}
            </Text>
          )}
        </div>
      </div>
      <div className='ml-10 max-h-[142px] overflow-hidden text-ellipsis break-words border-l-2 border-border pl-3 text-secondary-black-3'>
        {item.messages.map((message, index) => {
          return <TextHtml key={index} text={message.content} className='mt-3'></TextHtml>
        })}
      </div>
    </div>
  )
}
