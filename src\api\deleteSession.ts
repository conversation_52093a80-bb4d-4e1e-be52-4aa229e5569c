import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface DeleteSessionRequest {
  sessionId: string
}
export interface DeleteSessionResponse {
  result: 'ok' | string
}

export const deleteSession = async (data: DeleteSessionRequest): Promise<DeleteSessionResponse> => {
  const response = await fetchWithAuth<DeleteSessionResponse>(`${apiUrl}/session/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
