import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import { GetSubscriptionDetailResponse } from '@/api/subscription/getSubscriptionDetail'
import clsx from 'clsx'
import { TextEnum, Text } from '@/components/business/text'
import Image from 'next/image'
import { updatePayment } from '@/api/subscription/updatePayment'
import LoadingOverlay from '@/components/business/loading-overlay'
import { useEffect, useState } from 'react'

import { Environments, initializePaddle, Paddle, PaddleEventData } from '@paddle/paddle-js'
import { useRouter } from 'next/router'

const paddleEnv = process.env.NEXT_PUBLIC_PADDLE_ENV! as Environments
const paddleToken = process.env.NEXT_PUBLIC_PADDLE_CLIENT_TOKEN!

const BankDetail = ({
  subscriptionDetail,
}: {
  subscriptionDetail: GetSubscriptionDetailResponse | undefined
}) => {
  const { t } = useTranslation(Namespace.SUBSCRIPTION)
  const [loading, setLoading] = useState(false)
  const [paddle, setPaddle] = useState<Paddle>()
  const router = useRouter()

  const handleEditBank = async () => {
    setLoading(true)
    if (subscriptionDetail?.subscriptionId) {
      const res = await updatePayment({
        subscriptionId: subscriptionDetail?.subscriptionId,
      })
      if (paddle) {
        paddle.Checkout.open({
          transactionId: res.id,
        })
      }
    } else {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (!paddleEnv || !paddleToken) {
      throw Error('system error')
    }
    initializePaddle({
      environment: paddleEnv,
      token: paddleToken,
      eventCallback: (e: PaddleEventData) => {
        setLoading(false)
        // 订阅变更更新页面数据
        if (e.name === 'checkout.completed') {
          router.reload()
        }
      },
      checkout: {
        settings: {
          displayMode: 'overlay',
          allowedPaymentMethods: [
            'alipay',
            'apple_pay',
            'bancontact',
            'card',
            'google_pay',
            'ideal',
            'paypal',
          ],
        },
      },
    }).then((paddleInstance: Paddle | undefined) => {
      if (paddleInstance) {
        setPaddle(paddleInstance)
      }
    })
  }, [])

  return (
    <>
      {subscriptionDetail?.paymentMethod.card?.last4 && (
        <div
          className={clsx(
            'w-[338px] rounded-xl border border-primary bg-primary-bg px-7 py-[18px]',
          )}>
          <div className='flex justify-end'>
            <Image src='/images/bank.svg' alt='no data' width={33} height={20} />
          </div>

          <Text type={TextEnum.H4} className='mb-8 mt-[39px] font-medium' onClick={handleEditBank}>
            ****&nbsp;&nbsp;****&nbsp;&nbsp;****&nbsp;&nbsp;
            {subscriptionDetail?.paymentMethod.card?.last4}
          </Text>

          <Text
            type={TextEnum.H4}
            className='cursor-pointer font-medium text-primary'
            onClick={handleEditBank}>
            {t('paymentInfo.edit')}
          </Text>
        </div>
      )}
      <LoadingOverlay open={loading} />
    </>
  )
}

export default BankDetail
