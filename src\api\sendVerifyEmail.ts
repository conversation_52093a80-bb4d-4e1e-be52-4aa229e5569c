import { fetchWithAuth } from '@/lib/fetch'
import { SupportedLangs } from '@/i18n'
import { AuthError } from './types'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface SendVerifyEmailRequest {
  userId: string
  language: SupportedLangs
}

export interface SendVerifyEmailResponse {
  result: string
}

export const sendVerifyEmail = (data: SendVerifyEmailRequest) => {
  return fetchWithAuth<SendVerifyEmailResponse | AuthError>(`${apiUrl}/auth/sendVerifyEmail`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })
}
