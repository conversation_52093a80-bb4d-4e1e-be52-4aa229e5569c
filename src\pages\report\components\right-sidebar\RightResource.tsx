import { Text, TextEnum } from '@/components/business/text'
import Image from 'next/image'
import { useEffect, useRef, useState } from 'react'
import { listSavedSourceApi, deleteSourceApi } from '@/api/document/index'
import { Chat } from '../source-list/chat/Chat'
import { Pdf } from '../source-list/pdf/Pdf'
import { Media } from '../source-list/media/Media'
import { Overly } from '../source-list/Overly'
import clsx from 'clsx'
import EmptyContent from './EmptyContent'
import { ModalPreviewResource } from '../modal/ModalPreviewResource'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import { getReferenceList } from '@/api/getReferenceList'
import { SourceTypeEnum } from '@/api/report/saveSource'

export enum TypeEnum {
  CHAT = 'CHAT',
  PDF = 'PDF',
  MEDIA = 'MEDIA',
}

export const RightResource = () => {
  const { t } = useTranslation([Namespace.GENERATOR, Namespace.DEEPEXPLORE, Namespace.GLOBAL])
  const [selectId, setSelectId] = useState('')
  const [lastPageKey, setLastPageKey] = useState('')
  const [openPreview, setOpenPreview] = useState(false)
  const [deleteIndex, setDeleteIndex] = useState(null)
  const [sourceList, setSourceList] = useState([])
  const pageSizeRef = useRef({ page: 10 })
  const rightResourceRef = useRef(null)
  const loadMoreRef = useRef(null)
  const resourceRef = useRef({})
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingPage, setIsLoadingPage] = useState(true)

  const loadMore = async () => {
    if (!lastPageKey) return
    try {
      pageSizeRef.current.page += 10
      await getListSource(pageSizeRef.current.page)
    } catch (error) {
      console.error('获取数据失败:', error)
    }
  }
  const handleMouseEnter = (item) => {
    setSelectId(item.id)
  }
  const onCancelSave = async (item, index) => {
    setDeleteIndex(index)
    try {
      await deleteSourceApi({ id: item.id })
      sourceList.splice(index, 1)
      setDeleteIndex(null)
      setSelectId(null)
    } catch (error) {
      console.error('删除失败:', error)
    }
  }
  const onOpenDialog = async (item) => {
    if (item.type === SourceTypeEnum.CHAT) {
      const referenceList = await queryReferenceList(item.id)
      resourceRef.current = { ...item, referenceList }
    } else {
      resourceRef.current = item
    }
    setOpenPreview(true)
  }

  const queryReferenceList = async (sourceId: string) => {
    const referenceList = await getReferenceList({ sourceId })
    return referenceList
  }

  const getListSource = async (pageSize = pageSizeRef.current.page) => {
    const res = await listSavedSourceApi({ pageSize, lastPageKey: '' })
    setLastPageKey(res.pageKey)
    const list = res.items.map((item) => {
      return {
        ...item,
        render: () => {
          switch (item.type) {
            case TypeEnum.PDF:
              return <Pdf item={item} />
            case TypeEnum.MEDIA:
              return <Media item={item} />
            default:
              return <Chat item={item} />
          }
        },
        s3Url: item.s3Url || item.url,
      }
    })
    setSourceList(list)
    setIsLoadingPage(false)
  }

  useEffect(() => {
    getListSource()
  }, [])

  useEffect(() => {
    const ob = new IntersectionObserver(
      async (entries) => {
        if (isLoading) return
        if (entries[0].isIntersecting) {
          setIsLoading(true)
          await loadMore()
          setIsLoading(false)
        }
      },
      {
        root: rightResourceRef.current,
        threshold: 1,
      },
    )
    loadMoreRef.current && ob.observe(loadMoreRef.current)
  }, [loadMoreRef.current])

  return (
    <div
      className='hide-scrollbar h-screen-minus-header overflow-auto p-4 pt-9'
      ref={rightResourceRef}
      id='ai-smart-right-resource'>
      <Text type={TextEnum.H4} className='mb-4'>
        {t('generator:saveSource')}
      </Text>
      {isLoadingPage && (
        <div className='flex-center mt-[-100px] h-svh flex-col'>
          <Image
            src='/images/downloading.png'
            alt=''
            width={48}
            height={48}
            className='animate-spin360'
          />
          <Text type={TextEnum.Body_medium} className='mt-1 text-muted-foreground'>
            {t('deepExplore:status.loading')}
          </Text>
        </div>
      )}
      {sourceList.length ? (
        <>
          {sourceList.map((source, index) => {
            return (
              <div
                onMouseOver={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  handleMouseEnter(source)
                }}
                className={clsx('relative', index === deleteIndex ? 'fadeRight' : '')}
                key={source.id}>
                {source.render()}
                {source.id === selectId ? (
                  <Overly
                    onCancelSave={() => {
                      onCancelSave(source, index)
                    }}
                    onOpenDialog={() => {
                      onOpenDialog(source)
                    }}
                    setOverly={() => {
                      setSelectId(null)
                    }}></Overly>
                ) : null}
              </div>
            )
          })}
          {lastPageKey && (
            <div
              ref={loadMoreRef}
              className='mt-3 flex h-10 items-center justify-center bg-primary-bg'>
              <Text type={TextEnum.Body_small} className='mr-1'>
                {t('global:button.loadMore')}
              </Text>
              <Image
                src='/images/downloading.png'
                alt=''
                width={32}
                height={32}
                className='animate-spin360'
              />
            </div>
          )}
          <ModalPreviewResource
            open={openPreview}
            onClose={() => {
              getListSource()
              setOpenPreview(false)
            }}
            resource={resourceRef.current}></ModalPreviewResource>
        </>
      ) : (
        <div className='flex w-full flex-grow'>
          <EmptyContent></EmptyContent>
        </div>
      )}
    </div>
  )
}
