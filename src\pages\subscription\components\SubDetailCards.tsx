import { TextEnum, Text } from '@/components/business/text'
import InfoCard from './InfoCard'
import { Namespace, SupportedLangs } from '@/i18n'
import { useTranslation } from 'react-i18next'
import { formatTime } from '../utils/formatTime'
import { isBasicPlan, isMonthPlan } from '../utils/plan'
import { getPrice } from '../utils/getPrice'
import { GetSubscriptionDetailResponse } from '@/api/subscription/getSubscriptionDetail'
import { useUserStore } from '@/store/userStore'
import { SubscriptionEnum } from '@/types'

const SubDetailCards = ({
  subscriptionDetail,
  onChangePage,
}: {
  subscriptionDetail: GetSubscriptionDetailResponse | undefined
  onChangePage: () => void
}) => {
  const { t } = useTranslation(Namespace.SUBSCRIPTION)
  const user = useUserStore((state) => state.user)

  const currentPlan = subscriptionDetail?.currentPlan

  return (
    <>
      {currentPlan && (
        <div className='flex'>
          <InfoCard
            classes='rounded-l-2xl '
            header={{
              title: isBasicPlan(currentPlan) ? t('cardInfo.basic') : t('cardInfo.pro'),
              tagText: subscriptionDetail.status === 'active' ? t('tag.active') : t('tag.pastdue'),
              active: subscriptionDetail.status === 'active',
            }}
            content={{
              number: getPrice(currentPlan),
              text: isMonthPlan(currentPlan) ? t('common.monthText') : t('common.yearText'),
            }}
          />
          <InfoCard
            classes='border-x-0'
            header={{
              title: t('basicCard.quota'),
            }}
            content={{
              number: !isBasicPlan(currentPlan)
                ? t('basicCard.unlimited')
                : currentPlan === SubscriptionEnum.BASIC_MONTH
                  ? '100'
                  : '1200',
              text: t('basicCard.credits'),
              isText: !isBasicPlan(currentPlan),
            }}
          />
          {subscriptionDetail?.nextBilledAt === null ? (
            <InfoCard
              classes='rounded-r-2xl'
              header={{
                title: t('cardInfo.expires'),
              }}>
              <>
                <div className='mt-[22px] flex items-end gap-1'>
                  <Text type={TextEnum.H4}>
                    {subscriptionDetail?.curPeriodEndsAt
                      ? formatTime(
                          subscriptionDetail?.curPeriodEndsAt as number,
                          user?.setting.language as SupportedLangs,
                        )
                      : ''}
                  </Text>
                </div>
                <Text
                  type={TextEnum.H6}
                  className='cursor-pointer text-primary'
                  onClick={onChangePage}>
                  {t('basicCard.change')}
                </Text>
              </>
            </InfoCard>
          ) : (
            <InfoCard
              classes='rounded-r-2xl'
              header={{
                title: isMonthPlan(currentPlan)
                  ? t('basicCard.renewalMonthTitle')
                  : t('basicCard.renewalYearTitle'),
              }}>
              <>
                <div className='mt-[22px] flex items-end gap-1'>
                  <Text type={TextEnum.H4}>
                    {subscriptionDetail?.nextBilledAt
                      ? formatTime(
                          subscriptionDetail?.nextBilledAt as number,
                          user?.setting.language as SupportedLangs,
                        )
                      : ''}
                  </Text>
                </div>
                <Text
                  type={TextEnum.H6}
                  className='cursor-pointer text-primary'
                  onClick={onChangePage}>
                  {t('basicCard.change')}
                </Text>
              </>
            </InfoCard>
          )}
        </div>
      )}
    </>
  )
}

export default SubDetailCards
