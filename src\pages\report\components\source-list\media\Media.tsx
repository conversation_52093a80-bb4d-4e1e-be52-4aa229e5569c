import { TextHtml } from '../TextHtml'

import Image from 'next/image'
export const Media = ({ item }) => {
  return (
    <div className='mt-2 rounded-sm bg-primary-bg px-2 py-3'>
      <div className='relative h-[128px]'>
        <Image
          src={item.url}
          alt={`image-${item.id}`}
          layout='fill'
          fill
          sizes='360px'
          className='rounded-sm'
          style={{
            objectFit: 'scale-down',
          }}
          priority
          quality={100}></Image>
      </div>
      <div className='ml-10 max-h-[110px] overflow-hidden text-ellipsis break-words border-l-2 border-border pl-3 text-secondary-black-3'>
        {item.messages.map((message, index) => {
          return <TextHtml key={index} text={message.content} className='mt-3'></TextHtml>
        })}
      </div>
    </div>
  )
}
