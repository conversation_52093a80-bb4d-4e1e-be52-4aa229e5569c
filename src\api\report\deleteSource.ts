import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export type DeleteReportSourceResponse = {
  result: 'ok'
}

export const deleteReportSource = async (id: string): Promise<DeleteReportSourceResponse> => {
  const response = await fetchWithAuth<DeleteReportSourceResponse>(
    `${apiUrl}/report/deleteSource`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        id,
      }),
    },
  )

  return response
}
