/**
 * 追踪调试工具
 * 用于开发环境中测试和调试追踪功能
 */

import { trackingHighlight } from './tracking-highlight'

/**
 * 手动触发追踪高亮功能
 * 可以在浏览器控制台中调用此函数来测试追踪高亮功能
 */
export const debugTracking = {
  /**
   * 显示所有追踪元素
   */
  showTrackingElements() {
    if (typeof window === 'undefined') return

    console.log('[Tracking Debug] Manually showing tracking elements')
    trackingHighlight.startHighlighting()

    // 在控制台中打印帮助信息
    console.log(
      `
      %c追踪高亮调试工具%c
      
      按住 Control + Option + T (Mac) 或 Control + Alt + T (Windows) 可以高亮显示所有追踪元素
      
      您也可以使用以下命令手动控制高亮显示：
      - window.debugTracking.showTrackingElements() - 显示所有追踪元素
      - window.debugTracking.hideTrackingElements() - 隐藏所有追踪元素
    `,
      'background: #5661f6; color: white; padding: 4px 8px; border-radius: 4px; font-weight: bold;',
      '',
    )
  },

  /**
   * 隐藏所有追踪元素
   */
  hideTrackingElements() {
    if (typeof window === 'undefined') return

    console.log('[Tracking Debug] Manually hiding tracking elements')
    trackingHighlight.stopHighlighting()
  },
}

// 将调试工具添加到全局对象中，以便在控制台中访问
if (typeof window !== 'undefined') {
  window.debugTracking = debugTracking
}
