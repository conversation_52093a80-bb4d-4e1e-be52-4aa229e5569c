import { Modal } from '@/components/business/modal'
import { TextEnum, Text } from '@/components/business/text'
import { Button } from '@/components/ui/button'
import { Namespace } from '@/i18n'
import { Trans, useTranslation } from 'react-i18next'

export interface ContactUsModalModalProps {
  open: boolean
  onClose: () => void
}
const ContactUsModal: React.FC<ContactUsModalModalProps> = ({
  open,
  onClose,
}: ContactUsModalModalProps) => {
  const { t } = useTranslation(Namespace.GLOBAL)

  const handleCloseModal = () => {
    if (onClose) onClose()
  }

  return (
    <>
      <Modal open={open} className='w-[420px] break-all p-6' onClose={onClose}>
        <div>
          <Text type={TextEnum.H4}> {t('global:contactUs.title')}</Text>
          <Text type={TextEnum.Body_big} className='mt-4'>
            <Trans
              t={t}
              i18nKey='global:contactUs.content'
              components={{ email: <span className='text-primary' /> }}
            />
          </Text>
        </div>
        <div className='mt-5 flex w-full justify-end gap-2'>
          <Button onClick={handleCloseModal}>{t('global:button.confirm')}</Button>
        </div>
      </Modal>
    </>
  )
}

export default ContactUsModal
