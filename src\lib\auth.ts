import { GetServerSidePropsContext, NextPageContext } from 'next'
import { setCookie, smartiesDomain } from './cookie'

export const clearCookies = (ctx?: NextPageContext | GetServerSidePropsContext) => {
  const cookieConfig = {
    maxAge: 0,
    httpOnly: false,
    domain: smartiesDomain,
  }
  const cookieConfigAgain = {
    maxAge: 0,
    httpOnly: false,
  }
  // remove cookies
  setCookie('smartToken', '', cookieConfig, ctx)
  setCookie('smartToken', '', cookieConfigAgain, ctx)
}
