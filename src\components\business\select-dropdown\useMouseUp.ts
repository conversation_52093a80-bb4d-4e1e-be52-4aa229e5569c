/* eslint-disable @typescript-eslint/no-explicit-any */

import { useEffect, useCallback, useState } from 'react'

const SELECT_DROPDOWN_CHECH_ELEMENT_ID = [
  'ai-smarties-chat-content',
  'ai-smarties-pdf-chat-content',
  'ai-smarties-img-chat-content',
]
export type DropdownProps = {
  top: number
  left: number
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const useMouseUp = (props: any) => {
  const [dropdownPosition, setDropdownPosition] = useState<DropdownProps>({
    top: 0,
    left: 0,
  })
  // 获取选中的节点
  const getElement: any = () => {
    const text = window.getSelection()?.toString()
    if (!text?.trim().length) return
    const range = window.getSelection()!.getRangeAt(0)
    if (!range.toString()) return
    const rect = range.getBoundingClientRect()
    return rect
  }

  const [selectedText, setSelectedText] = useState('')
  const handleMouseUp = useCallback((e: MouseEvent) => {
    // 排除输入框
    const activeElement = document.activeElement
    if (
      activeElement &&
      (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')
    )
      return
    // 获取选中内容
    const text = window.getSelection()?.toString()
    if (text?.trim().length) {
      const elements = SELECT_DROPDOWN_CHECH_ELEMENT_ID.map((id) =>
        document.getElementById(id),
      ).filter((ele) => !!ele)
      if (!elements.length) return
      // 检测当前选中的内容是否在 可以选择的范围内
      if (!elements.map((ele) => ele.contains(e.target as Node)).filter((v) => !!v).length) return
      const rect = getElement()
      props.setIsOpen(true)

      setSelectedText(text)
      // 判断边界情况
      const viewportHeight = elements[0].offsetHeight
      const dropdownToTop = rect?.bottom - 64
      const dropdownHeight = 46
      const dropdownWidth = props.dropdownWidth / 2
      if (dropdownToTop + dropdownHeight > viewportHeight) {
        setDropdownPosition({
          top: rect?.top - dropdownHeight - 5 - window.scrollY,
          left: rect?.left + window.scrollX + rect?.width / 2 - dropdownWidth,
        })
      } else {
        setDropdownPosition({
          top: rect?.bottom + 5 + window.scrollY,
          left: rect?.left + window.scrollX + rect?.width / 2 - dropdownWidth,
        })
      }
    }
  }, [])

  useEffect(() => {
    document.addEventListener('mouseup', handleMouseUp)
    return () => {
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [])

  return { dropdownPosition, selectedText, setDropdownPosition, getElement }
}
