import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export enum QuickPDFInquiryEnum {
  DESCRIBE = 'DESCRIBE',
  OLD_QUESTION = 'OLD_QUESTION',
}

export interface PdfStartRequest {
  pdfId: string
  pdfSessionId?: string
  message?: string
  quickInquiry?: Omit<QuickPDFInquiryEnum, 'OLD_QUESTION'>
  source?: 'file' | 'pdf'
}

export type PdfStartResponse = {
  pdfProcessId: string
  pdfSessionId: string
}

export const pdfStart = async (data: PdfStartRequest): Promise<PdfStartResponse> => {
  const response = await fetchWithAuth<PdfStartResponse>(`${apiUrl}/pdf/pdfStart`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
