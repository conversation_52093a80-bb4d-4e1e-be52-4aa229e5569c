import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { CheckIcon } from '@heroicons/react/24/outline'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import { TextEnum, Text } from '@/components/business/text'

export interface DropdownMenusProps {
  list: {
    value: string
    key: string
  }[]
  children: string | React.ReactNode
  className: string
  open: boolean
  onClickDropItem: (item: { value: string; key: string }) => void
  onInteractOutside: () => void
  currentKey?: string
}

export const DropdownMenus: React.FC<DropdownMenusProps> = ({
  list = [],
  children,
  className,
  open,
  onClickDropItem,
  onInteractOutside,
  currentKey,
}) => {
  const { t } = useTranslation(Namespace.DEEPEXPLORE)
  return (
    <DropdownMenu modal={false} open={open}>
      <DropdownMenuTrigger asChild className={className}>
        {children}
      </DropdownMenuTrigger>
      <DropdownMenuContent className='w-[230px]' onInteractOutside={onInteractOutside}>
        {list.length &&
          list.map((item) => (
            <DropdownMenuItem
              key={item.key}
              className={`flex min-h-9 cursor-pointer items-center justify-between`}
              onClick={() => onClickDropItem(item)}>
              <Text type={TextEnum.Body_medium} className='w-44'>
                {t(`search.${item.value}`)}
              </Text>

              {item.key === currentKey && <CheckIcon className='h-5 w-5' />}
            </DropdownMenuItem>
          ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
