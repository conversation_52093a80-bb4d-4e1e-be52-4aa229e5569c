import { fetchWithAuth } from '@/lib/fetch'
import { PdfProcessItem } from './pdfProcess'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface PdfRunningRequest {
  pdfId: string
}

export type PdfRunningResponse = {
  pdfProcess: PdfProcessItem
}

export const pdfRunning = async (data: PdfRunningRequest): Promise<PdfRunningResponse> => {
  const response = await fetchWithAuth<PdfRunningResponse>(`${apiUrl}/pdf/pdfRunning`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
