import { useState, useEffect } from 'react'

const useIsMobileScreen = (threshold = 480) => {
  const [isMobile, setIsMobile] = useState(false)
  const [isSmallScreen, setIsSmallScreen] = useState(false)

  useEffect(() => {
    const checkScreenSize = () => {
      const isMobileNow = window.screen.width < threshold
      const isSmallScreen = window.innerWidth < threshold
      setIsMobile(isMobileNow)
      setIsSmallScreen(isSmallScreen)
    }

    checkScreenSize()

    const handleResize = () => {
      checkScreenSize()
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [threshold])

  return [isMobile, isSmallScreen]
}

export default useIsMobileScreen
