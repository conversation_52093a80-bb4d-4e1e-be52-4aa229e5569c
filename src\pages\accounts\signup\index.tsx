import { registerEmail, RegisterEmailResponse } from '@/api/registerEmail'
import { sendVerifyEmail } from '@/api/sendVerifyEmail'
import { AuthError } from '@/api/types'
import { updateSetting } from '@/api/updateSetting'
import { verifyRegister<PERSON>ode, VerifyRegisterCodeResponse } from '@/api/verifyRegisterCode'
import { AuthLogin } from '@/components/business/accounts/auth'
import { EmailLogin } from '@/components/business/accounts/email'
import { AccountLayout } from '@/components/business/accounts/layout'
import { ResendEmail } from '@/components/business/accounts/resend'
import { AccountTitle } from '@/components/business/accounts/title'
import { Text, TextEnum } from '@/components/business/text'
import useIsMobileScreen from '@/components/ui/useIsMobile'
import { Namespace, SupportedLangs } from '@/i18n'
import useLanguage from '@/i18n/useLanguage'
import { clearCookies } from '@/lib/auth'
import { getCookie, setCookie, smartiesDomain } from '@/lib/cookie'
import { smartiesRoutes } from '@/routers'
import { SubscriptionEnum } from '@/types'
import clsx from 'clsx'
import { Session } from 'next-auth'
import { getSession, signIn } from 'next-auth/react'
import { useRouter } from 'next/router'
import { ReactNode, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'sonner'
type PageStatus = 'init' | 'check' | 'verify'

const checkPassword = (password: string) => {
  if (!password) return false
  if (password.length < 8 || password.length > 20) return false
  const hasUpperCase = /[A-Z]/.test(password)
  const hasLowerCase = /[A-Z]/.test(password)
  const hasNumber = /[0-9]/.test(password)

  return hasUpperCase && hasLowerCase && hasNumber
}

const Signup = () => {
  const { t } = useTranslation([Namespace.GLOBAL, Namespace.LOGIN])
  const router = useRouter()
  const { query } = router
  const { type, code, action } = query
  const referralCode = query.referralCode as string | undefined
  const isVerifyCode = type === 'email' && code && action === 'verify'
  const [pageStatus, setPageStatus] = useState<PageStatus>(isVerifyCode ? 'verify' : 'init')
  const [remain, setRemain] = useState(3)
  const remainRef = useRef(3)
  const [loading, setLoading] = useState(true)
  const { language, changeLanguage } = useLanguage({ loggedIn: false })
  const userIdRef = useRef<string>()
  const redirectUrlRef = useRef<string>()
  const [email, setEmail] = useState<string>('')
  const [buttonDisabled, setButtonDisabled] = useState(false)
  const texts = {
    title: t('login:signup.title'),
    desc: t('login:signup.desc'),
    email: t('login:input.email'),
    emailPlaceholder: t('login:input.emailPlaceholder'),
    password: t('login:input.password'),
    passwordPlaceholder: t('login:input.passwordPlaceholder'),
    passwordTip: t('login:input.passwordTip'),
    button: t('login:signup.button'),
  }

  const isFetching = useRef(false)

  const handleLogin = async ({ type }: { type?: 'google' | 'linkedin' }) => {
    clearCookies()
    const baseUrl = redirectUrlRef.current ?? window.location.origin + '/' + smartiesRoutes.signup
    const callbackUrl = `${baseUrl}?auth=${type}${referralCode ? `&referralCode=${referralCode}` : ''}`
    await signIn(type, { callbackUrl, referralCode })
  }

  useEffect(() => {
    // Tracking.trackPageView({ eventName: 'VISIT_SIGNUP_PAGE' })
    console.log('0', router.pathname, router.query?.auth)
    if (!query.auth) {
      setLoading(false)
      console.log('1')
      return
    }
    console.log('2')
    if (!isFetching.current) {
      isFetching.current = true
      console.log('3')
      getSession()
        .then((data) => {
          console.log('4')
          const sessionData = data as Session & {
            smartiesToken?: string
            userId: string
            quota: number
            usage: number
            currentPlan: SubscriptionEnum
          }
          if (sessionData?.smartiesToken && sessionData.user) {
            console.log('5')
            setCookie('smartToken', sessionData.smartiesToken, {
              maxAge: 7 * 24 * 60 * 60 * 1000,
              httpOnly: false,
              domain: smartiesDomain,
            })
            updateSetting({
              language: (language ?? getCookie('lang')) as SupportedLangs,
            }).then(() => {
              console.log('6')
              isFetching.current = false
              router.push(smartiesRoutes.basicSearch.home)
            })
          } else {
            console.log('7')
            isFetching.current = false
            setLoading(false)
          }
        })
        .catch(() => {
          console.log('8')
          isFetching.current = false
          setLoading(false)
        })
    }
  }, [])

  useEffect(() => {
    if (query?.lang && query?.redirectUrl) {
      changeLanguage(query.lang as string)
      redirectUrlRef.current = query.redirectUrl as string
    }
    if (pageStatus === 'verify') {
      verifyRegisterCode({ code: query.code as string }).then((res) => {
        if ((res as AuthError).errCode) {
          toast((res as AuthError).errMsg, {
            duration: 3000,
          })
        } else {
          setCookie('smartToken', (res as VerifyRegisterCodeResponse).accessToken, {
            maxAge: 7 * 24 * 60 * 60 * 1000,
            httpOnly: false,
            domain: smartiesDomain,
          })
          const timer = setInterval(() => {
            remainRef.current = remainRef.current - 1
            if (remainRef.current <= 0) {
              clearInterval(timer)
              router.push({
                pathname: smartiesRoutes.basicSearch.home,
                query: {
                  redirectSource: 'signup',
                },
              })
            }
            setRemain(remainRef.current)
          }, 1000)
        }
      })
    }
  }, [])

  const handleOpenTerms = () => {
    window.open('/policies/terms-of-use/index.html', '_blank')
  }

  const handleOpenPrivacy = () => {
    window.open('/policies/privacy-policy/index.html', '_blank')
  }

  const [isMobile] = useIsMobileScreen()

  // 语言不走json包，这个是固定的
  // 语言选择框，不跟 mode走，因为首页是固定深色的
  return loading ? null : (
    <AccountLayout isIframe={!!query?.lang && !!query?.redirectUrl}>
      {pageStatus === 'init' ? (
        <>
          <AccountTitle title={texts.title} desc={texts.desc} />
          <div className='flex-center w-full flex-col' asm-tracking='TEST_VISIT_SIGNUP_PAGE:VIEW'>
            <EmailLogin
              texts={texts}
              buttonDisabled={buttonDisabled}
              suffix={
                <Text type={TextEnum.Body_medium} className={'w-full !leading-6 text-white'}>
                  {t('login:policy.p1')}
                  <span className='cursor-pointer text-primary' onClick={handleOpenTerms}>
                    &nbsp; {t('login:policy.terms')} &nbsp;
                  </span>
                  {t('login:policy.p2')}
                  <span className='cursor-pointer text-primary' onClick={handleOpenPrivacy}>
                    &nbsp; {t('login:policy.privacy')}&nbsp;
                  </span>
                </Text>
              }
              onClick={(email, password) => {
                ;(async () => {
                  setEmail(email)
                  setButtonDisabled(true)
                  if (!checkPassword(password)) {
                    toast(t('login:toast.password'), {
                      duration: 3000,
                    })
                    return
                  }
                  const res = await registerEmail({
                    email,
                    password,
                    language: language as SupportedLangs,
                    referralCode,
                  })

                  if ((res as AuthError).errCode) {
                    const response = res as AuthError
                    let description: ReactNode = response.errMsg
                    if (response.errCode === 'invalidPassword') {
                      description = t('login:toast.password')
                    }
                    if (response.errCode === 'emailAlreadyExisted') {
                      description = (
                        <Text type={TextEnum.Body_medium}>
                          {`${t('login:toast.account')}`}
                          <span
                            className='cursor-pointer text-primary'
                            onClick={() => router.push(smartiesRoutes.login)}>
                            &nbsp; {t('login:account.action').toLocaleLowerCase()} &nbsp;
                          </span>
                        </Text>
                      )
                    }
                    toast(description, {
                      duration: 3000,
                    })
                  } else {
                    userIdRef.current = (res as RegisterEmailResponse).userId
                    setPageStatus('check')
                  }
                })().finally(() => setButtonDisabled(false))
              }}
            />
            <div className={clsx('flex-center', isMobile ? 'w-11/12' : 'w-[420px]')}>
              <AuthLogin onAuthLogin={handleLogin} />
            </div>
            <Text
              type={TextEnum.Body_medium}
              className={clsx(
                'mt-10 w-[420px] text-center !leading-6 text-white',
                isMobile ? 'mt-2' : 'mt-10',
              )}>
              {t('login:account.status')}
              <span
                className='cursor-pointer text-primary'
                onClick={() => router.push({ pathname: smartiesRoutes.login, query })}>
                &nbsp; {t('login:account.action')} &nbsp;
              </span>
            </Text>
          </div>
        </>
      ) : null}
      {pageStatus === 'check' ? (
        <ResendEmail
          email={email}
          desc={t('login:reactive', {
            email: email,
          })}
          disabled={buttonDisabled}
          onResend={() => {
            ;(async () => {
              setButtonDisabled(true)
              const res = await sendVerifyEmail({
                userId: userIdRef.current!,
                language: language as SupportedLangs,
              })
              if ((res as AuthError).errCode) {
                toast((res as AuthError).errMsg, {
                  duration: 3000,
                })
              } else {
                toast(t('login:toast.resend'), {
                  duration: 3000,
                })
              }
            })().finally(() => setButtonDisabled(false))
          }}
        />
      ) : null}
      {pageStatus === 'verify' ? (
        <div className='w-full'>
          <div className={clsx(isMobile ? 'flex justify-center' : '')}>
            <AccountTitle title={t('login:back.title')} desc={t('login:back.desc')} />
          </div>
          <Text type={TextEnum.H3} className='text-center text-white'>
            {remain}s
          </Text>
        </div>
      ) : null}
    </AccountLayout>
  )
}

export default Signup
