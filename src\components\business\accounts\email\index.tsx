import { useState, ReactNode } from 'react'
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import clsx from 'clsx'
import useIsMobileScreen from '@/components/ui/useIsMobile'

interface ITexts {
  email: string
  emailPlaceholder: string
  password: string
  passwordPlaceholder: string
  button: string
}

interface IEmailLoginProps {
  texts: ITexts
  suffix: ReactNode
  onClick?: (email: string, password: string) => void
  buttonDisabled?: boolean
}

export const EmailLogin = (props: IEmailLoginProps) => {
  const { texts, suffix, onClick, buttonDisabled = false } = props
  const [passwordVisible, setPasswordVisible] = useState(false)
  const [password, setPassword] = useState('')
  const [email, setEmail] = useState('')

  const [isMobile] = useIsMobileScreen()

  return (
    <div
      className={clsx('flex-center flex-col', isMobile ? 'w-11/12' : 'w-[420px]')}
      data-component='EmailLogin'>
      <div className='flex w-full flex-col gap-2.5'>
        <div className='text-sm font-normal text-white'>{texts.email}</div>
        <Input
          value={email}
          placeholder={texts.emailPlaceholder}
          className='bg-transparent text-white'
          onChange={(event) => setEmail(event.target.value)}
        />
      </div>
      <div className='mb-5 mt-[15px] flex w-full flex-col gap-2.5'>
        <div className='text-sm font-normal text-white'>{texts.password}</div>
        <div className='flex-center relative flex w-full'>
          <Input
            value={password}
            type={passwordVisible ? 'text' : 'password'}
            placeholder={texts.passwordPlaceholder}
            className='bg-transparent pr-[60px] text-white'
            onChange={(event) => setPassword(event.target.value)}
          />
          {passwordVisible ? (
            <EyeIcon
              className='absolute right-[20px] cursor-pointer text-white'
              width={20}
              height={20}
              onClick={() => setPasswordVisible(false)}
            />
          ) : (
            <EyeSlashIcon
              width={20}
              height={20}
              className='absolute right-[20px] cursor-pointer text-white'
              onClick={() => setPasswordVisible(true)}
            />
          )}
        </div>
      </div>
      {suffix}
      <Button
        className={clsx(
          'mb-5 w-full cursor-pointer rounded-sm bg-primary hover:bg-primary disabled:bg-primary-disabled disabled:opacity-100',
          isMobile ? 'mt-1' : 'mt-10',
        )}
        disabled={!(email && password) || buttonDisabled}
        onClick={() => onClick?.(email, password)}>
        {texts.button}
      </Button>
    </div>
  )
}
