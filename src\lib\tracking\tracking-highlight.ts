import { parseElementTrackingInfo } from './common'

// 检查是否已初始化
let isInitialized = false

/**
 * 追踪元素高亮功能
 * 用于在开发环境中可视化页面上的追踪元素
 */
export const trackingHighlight = {
  // 高亮状态
  isHighlighting: false,

  // 高亮元素的集合
  highlightedElements: new Set<HTMLElement>(),

  // 信息提示元素
  infoElements: new Map<HTMLElement, HTMLElement>(),

  // 无埋点提示元素
  noTrackingElement: null as HTMLElement | null,

  // 主题色
  themeColor: '#5661f6', // 使用 --primary 变量的值

  /**
   * 初始化追踪高亮功能
   */
  init() {
    if (typeof window === 'undefined' || isInitialized) return

    // 添加键盘事件监听
    document.addEventListener('keydown', this.handleKeyDown.bind(this))
    document.addEventListener('keyup', this.handleKeyUp.bind(this))

    // 标记为已初始化
    isInitialized = true

    if (process.env.NODE_ENV !== 'production') {
      console.log('[Tracking-Highlight] Initialized')
    }
  },

  /**
   * 处理键盘按下事件
   */
  handleKeyDown(event: KeyboardEvent) {
    // Mac: Control + Option + T
    // Windows: Control + Alt + T
    // const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0

    if (event.key?.toLowerCase() === 't' && event.ctrlKey) {
      if (!this.isHighlighting) {
        this.startHighlighting()
      }
    }
  },

  /**
   * 处理键盘释放事件
   */
  handleKeyUp(event: KeyboardEvent) {
    // 当任何按键释放时，如果不再满足组合键条件，则停止高亮
    const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0

    if (
      event.key?.toLowerCase() === 't' ||
      !event.ctrlKey ||
      !(isMac ? event.altKey : event.altKey)
    ) {
      if (this.isHighlighting) {
        this.stopHighlighting()
      }
    }
  },

  /**
   * 开始高亮追踪元素
   */
  startHighlighting() {
    this.isHighlighting = true

    // 查找所有带有 asm-tracking 属性的元素
    const elements = document.querySelectorAll('[asm-tracking]')

    if (elements.length === 0) {
      this.showNoTrackingMessage()
      return
    }

    // 为每个元素添加高亮样式
    elements.forEach((element) => {
      this.highlightElement(element as HTMLElement)
    })
  },

  /**
   * 停止高亮追踪元素
   */
  stopHighlighting() {
    this.isHighlighting = false

    // 移除所有高亮样式
    this.highlightedElements.forEach((element) => {
      element.style.outline = ''
      element.style.outlineOffset = ''
      element.style.boxShadow = ''
    })
    this.highlightedElements.clear()

    // 移除所有信息提示元素
    this.infoElements.forEach((infoElement) => {
      document.body.removeChild(infoElement)
    })
    this.infoElements.clear()

    // 移除无埋点提示
    if (this.noTrackingElement && document.body.contains(this.noTrackingElement)) {
      document.body.removeChild(this.noTrackingElement)
      this.noTrackingElement = null
    }
  },

  /**
   * 高亮单个元素
   */
  highlightElement(element: HTMLElement) {
    // 添加高亮样式 - 使用更轻量的样式
    element.style.outline = `2px solid ${this.themeColor}`
    element.style.outlineOffset = '2px'
    element.style.boxShadow = `0 0 5px rgba(86, 97, 246, 0.5)`
    this.highlightedElements.add(element)

    // 创建并显示信息提示
    this.showElementInfo(element)
  },

  /**
   * 显示元素信息提示
   */
  showElementInfo(element: HTMLElement) {
    const trackingInfo = parseElementTrackingInfo(element)
    if (!trackingInfo) return

    // 创建信息提示元素 - 更轻量的设计
    const infoElement = document.createElement('div')
    infoElement.style.position = 'absolute'
    infoElement.style.backgroundColor = 'rgba(86, 97, 246, 0.9)'
    infoElement.style.color = 'white'
    infoElement.style.padding = '6px 10px'
    infoElement.style.borderRadius = '3px'
    infoElement.style.fontSize = '12px'
    infoElement.style.lineHeight = '1.4'
    infoElement.style.zIndex = '9999'
    infoElement.style.maxWidth = '250px'
    infoElement.style.boxShadow = '0 1px 4px rgba(0, 0, 0, 0.1)'
    infoElement.style.fontWeight = '400'
    infoElement.style.backdropFilter = 'blur(2px)'
    infoElement.style.border = '1px solid rgba(255, 255, 255, 0.2)'

    // 构建信息内容 - 更精简的展示
    let infoContent = `
      <div style="font-weight: 600; margin-bottom: 3px; font-size: 12px; color: rgba(255,255,255,0.95);">${trackingInfo.eventName}</div>
      <div style="margin-bottom: 3px; font-size: 11px; color: rgba(255,255,255,0.85);">触发: ${trackingInfo.eventType}</div>
    `

    // 添加自定义方法信息
    if (trackingInfo.customMethod) {
      infoContent += `<div style="margin-bottom: 3px; font-size: 11px; color: rgba(255,255,255,0.85);">方法: ${trackingInfo.customMethod}</div>`
    }

    // 添加参数信息
    if (Object.keys(trackingInfo.params).length > 0) {
      infoContent += `<div style="margin-top: 2px; font-size: 11px; font-weight: 600; color: rgba(255,255,255,0.9);">参数:</div>`

      for (const [key, value] of Object.entries(trackingInfo.params)) {
        infoContent += `<div style="margin-left: 4px; margin-bottom: 1px; font-size: 10px; color: rgba(255,255,255,0.8);">${key}: <span style="color: rgba(255,255,255,0.9);">${value}</span></div>`
      }
    }

    infoElement.innerHTML = infoContent

    // 添加到文档中
    document.body.appendChild(infoElement)

    // 计算位置
    this.positionInfoElement(element, infoElement)

    // 保存引用
    this.infoElements.set(element, infoElement)
  },

  /**
   * 计算并设置信息提示元素的位置
   */
  positionInfoElement(targetElement: HTMLElement, infoElement: HTMLElement) {
    const rect = targetElement.getBoundingClientRect()
    const infoRect = infoElement.getBoundingClientRect()

    // 默认显示在元素右侧
    let left = rect.right + 10
    let top = rect.top

    // 检查是否超出视窗右边界
    if (left + infoRect.width > window.innerWidth) {
      // 如果超出，则显示在元素左侧
      left = rect.left - infoRect.width - 10

      // 如果左侧也不行，则显示在元素上方或下方
      if (left < 0) {
        left = Math.max(0, rect.left)
        top = rect.bottom + 10

        // 如果下方也不行，则显示在上方
        if (top + infoRect.height > window.innerHeight) {
          top = Math.max(0, rect.top - infoRect.height - 10)
        }
      }
    }

    // 检查是否超出视窗底部
    if (top + infoRect.height > window.innerHeight) {
      top = window.innerHeight - infoRect.height - 10
    }

    // 检查是否超出视窗顶部
    if (top < 0) {
      top = 10
    }

    // 设置位置
    infoElement.style.left = `${left + window.scrollX}px`
    infoElement.style.top = `${top + window.scrollY}px`
  },

  /**
   * 显示无埋点提示
   */
  showNoTrackingMessage() {
    // 创建提示元素 - 更轻量的设计
    const noTrackingElement = document.createElement('div')
    noTrackingElement.style.position = 'fixed'
    noTrackingElement.style.right = '16px'
    noTrackingElement.style.bottom = '16px'
    noTrackingElement.style.backgroundColor = 'rgba(0, 0, 0, 0.6)'
    noTrackingElement.style.color = 'rgba(255, 255, 255, 0.9)'
    noTrackingElement.style.padding = '6px 10px'
    noTrackingElement.style.borderRadius = '3px'
    noTrackingElement.style.fontSize = '12px'
    noTrackingElement.style.fontWeight = '500'
    noTrackingElement.style.zIndex = '9999'
    noTrackingElement.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.2)'
    noTrackingElement.style.backdropFilter = 'blur(2px)'
    noTrackingElement.style.border = '1px solid rgba(255, 255, 255, 0.1)'
    noTrackingElement.textContent = 'no-tracking-point'

    // 添加到文档中
    document.body.appendChild(noTrackingElement)

    // 保存引用
    this.noTrackingElement = noTrackingElement
  },
}
