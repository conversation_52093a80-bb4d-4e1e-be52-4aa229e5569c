/* eslint-disable @typescript-eslint/no-explicit-any */
// tracking.ts

// import mixpanel from 'mixpanel-browser'
import { useUserStore } from '@/store/userStore'
// 初始化 Mixpanel
// 注意：在实际使用时，你需要将 token 放在环境变量中
const MIXPANEL_TOKEN = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN || ''
const getVersion = () => {
  // 获取当前时间
  const now = new Date()
  // 获取当前年份
  const year = now.getFullYear()
  // 获取当前月份
  const month = now.getMonth() + 1
  // 获取当前日期
  const day = now.getDate()
  return `${year}${month}${day}`
}
// 只在客户端初始化 Mixpanel
if (typeof window !== 'undefined') {
  mixpanel.init(MIXPANEL_TOKEN, {
    debug: process.env.NODE_ENV !== 'production',
    track_pageview: false, // 我们会手动追踪页面浏览
    ignore_dnt: true, // 忽略 "Do Not Track"（DNT）设置
  })
}

type TrackPageViewProps = {
  pageName: string
  test?: boolean
  properties?: Record<string, any>
}
const isDev = process.env.NEXT_PUBLIC_SYSTEM_ENV === 'dev'
export const Tracking = {
  trackPageView: async ({ eventName, test = true, properties = {} }: TrackPageViewProps) => {
    if (isDev) return

    try {
      // 创建一个获取用户信息的 Promise
      const getUserPromise = new Promise((resolve) => {
        const checkUser = () => {
          const { user } = useUserStore.getState()
          if (user) {
            resolve(user)
          } else {
            setTimeout(checkUser, 100) // 每100ms检查一次
          }
        }
        checkUser()
      })

      // 创建一个超时 Promise
      const timeoutPromise = new Promise((resolve) => {
        setTimeout(() => resolve(null), 1500) // 3秒超时
      })

      // 等待用户信息或超时
      const user = await Promise.race([getUserPromise, timeoutPromise])

      eventName = test ? `TEST_${eventName}` : eventName
      console.info('处理埋点：', eventName, properties)

      window.mixpanel.track(eventName, {
        version: getVersion(),
        user_id: user?.userId,
        page_url: window.location.href,
        role: user?.currentPlan,
        ...properties,
      })
    } catch (error) {
      console.error('Error tracking page view:', error)
    }
  },

  // 可以在这里添加更多的跟踪方法
}
