import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface GetInvoiceRequest {
  transactionId: string
}

export interface GetInvoiceResponse {
  url: string
}

export const getInvoice = async (data: GetInvoiceRequest): Promise<GetInvoiceResponse> => {
  const response = await fetchWithAuth<GetInvoiceResponse>(`${apiUrl}/transaction/getInvoice`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
