import NextAuth from 'next-auth'
import Google from 'next-auth/providers/google'
import LinkedIn from 'next-auth/providers/linkedin'
import { JWT } from 'next-auth/jwt'
import { InvokeCommand, LambdaClient, LogType } from '@aws-sdk/client-lambda'
import { NextApiRequest, NextApiResponse } from 'next'

export interface LoginResponse {
  success: boolean
  accessToken: string
  name: string
  email: string
  userId: string
}

export enum OAuthLoginEnum {
  GOOGLE = 'GOOGLE',
  LINKEDIN = 'LINKEDIN',
}

const {
  NEXTAUTH_SECRET,
  LAMBDA_ACCOUNT,
  LAMBDA_REGION,
  LAMBDA_ACCESS_KEY,
  LAMBDA_SECRET_ACCESS_KEY,
  ENV,
} = process.env

const client = LAMBDA_ACCOUNT
  ? new LambdaClient({
      region: LAMBDA_REGION,
      credentials: {
        accessKeyId: LAMBDA_ACCESS_KEY!,
        secretAccessKey: LAMBDA_SECRET_ACCESS_KEY!,
      },
    })
  : new LambdaClient({})

const invokeLogin = async ({
  name,
  email,
  picture,
  source,
  referralCode,
  anonymousId,
}: {
  name: string
  email: string
  picture: string
  source: OAuthLoginEnum
  referralCode?: string
  anonymousId?: string
}) => {
  console.log(
    'start invoke lmabda',
    JSON.stringify({
      name,
      email,
      picture,
      source,
      referralCode,
      anonymousId,
    }),
  )
  if (!ENV) throw Error('ENV is empty!')

  const command = new InvokeCommand({
    FunctionName: `digiSmarties-${ENV}-loginOauth`,
    Payload: JSON.stringify({
      userInfo: {
        name: name,
        email: email,
        picture: picture,
        referralCode,
        anonymousId,
      },
      source,
    }),
    LogType: LogType.None,
  })

  const { Payload } = await client.send(command)
  const data = Buffer.from(Payload || '').toString()
  const res = JSON.parse(data) as LoginResponse

  return res
}

export default async function auth(req: NextApiRequest, res: NextApiResponse) {
  return await NextAuth(req, res, {
    // Configure one or more authentication providers
    debug: true,
    providers: [
      Google({
        clientId: process.env.AUTH_GOOGLE_ID!,
        clientSecret: process.env.AUTH_GOOGLE_SECRET!,
        authorization: {
          params: {
            prompt: 'consent',
            access_type: 'offline',
            response_type: 'code',
          },
        },
        httpOptions: { timeout: 5000 },
        checks: ['none'],
      }),
      LinkedIn({
        clientId: process.env.LINKEDIN_CLIENT_ID!,
        clientSecret: process.env.LINKEDIN_CLIENT_SECRET!,
        issuer: 'https://www.linkedin.com/oauth',
        jwks_endpoint: 'https://www.linkedin.com/oauth/openid/jwks',
        userinfo: {
          url: 'https://api.linkedin.com/v2/userinfo',
        },
        authorization: {
          params: {
            scope: 'profile email openid',
            prompt: 'consent',
            access_type: 'offline',
            response_type: 'code',
          },
        },
        token: {
          url: 'https://www.linkedin.com/oauth/v2/accessToken',
        },
        async profile(profile) {
          return {
            id: profile.sub,
            name: profile.name,
            firstname: profile.given_name,
            lastname: profile.family_name,
            email: profile.email,
            image: profile.picture,
            referralCode: profile.referralCode,
          }
        },
      }),
      // ...add more providers here
    ],
    callbacks: {
      async jwt({ token, account, profile }): Promise<JWT> {
        console.log(
          'callbacks',
          JSON.stringify(account),
          JSON.stringify(profile),
          JSON.stringify(token),
        )
        // 如果没有 `account` 和 `profile`，说明是重用会话
        if (!account) {
          //
        }

        // 初次授权时，保存 appToken
        if (account && profile) {
          try {
            const res = await invokeLogin({
              name: token.name!,
              email: token.email!,
              picture: token.picture!,
              referralCode: token.referralCode as string | undefined,
              source: account.provider.toUpperCase() as OAuthLoginEnum,
              anonymousId: req.cookies?.['anonymousId'] || '',
            })

            // Save the appToken in the JWT token
            if (res.success) {
              token.smartiesToken = res.accessToken
              token.userId = res.userId
            }
          } catch (error) {
            console.log('invokeLogin error', error)
            token.smartiesToken = ''
            token.userId = ''
          }
        }
        return token
      },
      async session({ session, token }) {
        console.log('session', JSON.stringify(session), JSON.stringify(token))
        if (token && session.user) {
          // Pass the app token to the session so it can be accessed on the front-end
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-expect-error
          session.smartiesToken = token.smartiesToken! // Add your app's token
          // Pass the app token to the session so it can be accessed on the front-end
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-expect-error
          session.userId = token.userId! // Add your app's token
        }
        return session
      },
    },
    secret: NEXTAUTH_SECRET,
  })
}
