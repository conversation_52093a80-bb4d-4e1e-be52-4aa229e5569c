import { Modal } from '@/components/business/modal'
import { But<PERSON> } from '@/components/ui/button'
import { Namespace } from '@/i18n'
import { useEffect, useState } from 'react'
import { Text, TextEnum } from '@/components/business/text'
import { useTranslation } from 'react-i18next'
import { XMarkIcon } from '@heroicons/react/24/solid'
import { Textinput } from '@/components/business/text-input'
import { getTemplateApi, updateTemplateApi } from '@/api/document/index'
import { useDocEmpty } from '../../hooks/useDocEmpty'
import { DocumentTextIcon } from '@heroicons/react/24/outline'
import clsx from 'clsx'
import { Skeleton } from '@/components/ui/skeleton'

// 需要获取最新的模板
export const ModalSetting = ({ open, handleCancel }) => {
  const { t } = useTranslation([Namespace.GLOBAL, Namespace.GENERATOR])
  const [inputValue, setInputValue] = useState()
  const docList = useDocEmpty()
  const currentDocList = docList.filter((doc) => doc.type)
  const [currentTemplate, setCurrentTemplate] = useState(currentDocList[0])
  const [isLoading, setIsLoading] = useState(false)
  const changeType = (item) => {
    setCurrentTemplate(item)
    getLastTemplate(item.type)
  }
  const handleConfirm = async () => {
    await updateTemplateApi({ type: currentTemplate.type, content: inputValue })
    handleCancel()
  }
  const handleChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value)
  }
  const getLastTemplate = async (type) => {
    setIsLoading(true)
    const data = await getTemplateApi({ type })
    setInputValue(data.content)
    setIsLoading(false)
  }
  useEffect(() => {
    if (open) {
      setIsLoading(true)
      getLastTemplate(currentDocList[0].type)
      setCurrentTemplate(currentDocList[0])
    }
  }, [open])
  return (
    <Modal open={open} onClose={handleCancel} className='w-[1000px] break-all'>
      <div>
        <Text
          type={TextEnum.H4}
          className='flex items-center justify-between border-b border-border p-5'>
          {t('generator:docSetting')}
          <XMarkIcon onClick={handleCancel} className='h-5 w-5 cursor-pointer' />
        </Text>
        <div className='flex h-[600px]'>
          <div className='w-60 shrink-0 border-r border-border'>
            <ul className='mx-3 mt-4'>
              {currentDocList.map((doc) => {
                return (
                  <li
                    className={clsx(
                      'mb-2 flex cursor-pointer items-center rounded-sm px-4 py-3 hover:bg-secondary-hover',
                      doc.type === currentTemplate.type ? 'bg-secondary-hover' : '',
                    )}
                    onClick={() => {
                      changeType(doc)
                    }}
                    key={doc.key}>
                    <DocumentTextIcon className='mr-1.5 h-5 w-5 shrink-0'></DocumentTextIcon>{' '}
                    <Text type={TextEnum.Body_small} className='shrink-0 translate-y-[1px]'>
                      <span>{doc.name}</span>
                    </Text>
                  </li>
                )
              })}
            </ul>
          </div>
          <div className='flex-1'>
            <div className='w-full px-16 pt-8'>
              <Text type={TextEnum.H4} className='mb-1.5'>
                {currentTemplate.name}
              </Text>
              <Text type={TextEnum.Body_medium} className='mb-6 text-muted-foreground'>
                {t('generator:settingTips')}
              </Text>
              {isLoading ? (
                <Skeleton className='h-[400px] w-[600px]' />
              ) : (
                <Textinput
                  maxHeight={410}
                  isStopEnterPrevent={false}
                  className='flex-1 shrink-0 border-border'
                  value={inputValue}
                  onChange={handleChange}
                />
              )}
            </div>
            <div className='absolute bottom-0 right-0 mt-5 flex py-5 pr-6'>
              <Button onClick={handleCancel} variant='secondary'>
                {t('button.cancel')}
              </Button>
              <Button onClick={handleConfirm} className='ml-2'>
                {t('button.confirm')}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  )
}
