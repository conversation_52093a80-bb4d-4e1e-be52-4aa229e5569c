import * as React from 'react'
import { Text, TextEnum } from '@/components/business/text'
import { TextTips } from '@/components/business/text-tips'
import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/router'
import { smartiesRoutes } from '@/routers'
import { Button } from '@/components/ui/button'
import { PaperAirplaneIcon } from '@heroicons/react/24/outline'
import { startQuest } from '@/api/startQuest'
import { PoTypeEnum, SearchModeEnum } from '@/types'
import { useEffect, useState } from 'react'
import CreditsModal from '@/components/business/credits-modal/CreditsModal'
import { Textinput } from '@/components/business/text-input'
import { checkCredits } from '../management'
import { useUserStore } from '@/store/userStore'
import SearchModeSelect from '@/components/business/searh-mode-select'

const OverseaRegulation = () => {
  const { t } = useTranslation(Namespace.BASIC_SEARCH)
  const router = useRouter()

  const user = useUserStore((state) => state.user)

  const [description, setDescription] = React.useState<string>('')
  const [targetMarket, setTargetMarket] = React.useState<string>('')
  const [currentLocation, setCurrentLocation] = React.useState<string>('')

  const [disableSend, setDisableSend] = useState(false)
  const [sended, setSended] = useState(false)
  const [openCreditsModal, setOpenCreditsModal] = useState(false)
  const [searchMode, setSearchMode] = useState<SearchModeEnum>(SearchModeEnum.AUTO)

  useEffect(() => {
    if (description.trim() && targetMarket.trim() && currentLocation.trim()) {
      setDisableSend(false)
    } else {
      setDisableSend(true)
    }
  }, [description, targetMarket, currentLocation])

  const handleClickSend = async () => {
    if (!disableSend && !sended) {
      const checkRes = checkCredits(user)
      if (!checkRes) {
        setOpenCreditsModal(true)
        return false
      }

      setSended(true)
      try {
        const resp = await startQuest({
          message: [
            {
              key: 'description',
              value: description,
            },
            {
              key: 'targetMarket',
              value: targetMarket,
            },
            {
              key: 'currentLocation',
              value: currentLocation,
            },
          ],
          poType: PoTypeEnum.REGULATION,
          useReasoning: searchMode === SearchModeEnum.REASONING ? true : undefined,
        })
        if (resp.sessionId && resp.processId) {
          router.push({
            pathname: smartiesRoutes.basicSearch.chat(PoTypeEnum.REGULATION.toLocaleLowerCase()),
            query: {
              processId: resp.processId,
              sessionId: resp.sessionId,
            },
          })
        } else {
          setSended(false)
        }
      } catch (error) {
        // TODO toast 请求异常，请稍后
        console.error('query', error)
        setSended(false)
      }
    }
  }

  const handleQueryChange = (
    key: 'description' | 'targetMarket' | 'currentLocation',
    value: string,
  ) => {
    if (key === 'description') {
      setDescription(value)
    }
    if (key === 'targetMarket') {
      setTargetMarket(value)
    }
    if (key === 'currentLocation') {
      setCurrentLocation(value)
    }
  }

  return (
    <div className='flex w-full min-w-[500px] max-w-[760px] flex-col gap-2.5'>
      <TextTips title={t('homePage.card.title_regulation')} tips={t('oversea.tips1')} size='big' />
      <Text type={TextEnum.H5}> {t('oversea.query1')}</Text>
      <Textinput
        onChange={(e) => {
          handleQueryChange('description', e.target.value)
        }}
        placeholder={t('oversea.query1Placeholder')}
        maxLength={5000}
        withIcon={false}
        value={description}
        onEnter={handleClickSend}
      />

      <Text type={TextEnum.H5}>{t('oversea.query2')}</Text>
      <Textinput
        onChange={(e) => {
          handleQueryChange('targetMarket', e.target.value)
        }}
        placeholder={t('oversea.query2Placeholder')}
        maxLength={300}
        withIcon={false}
        value={targetMarket}
        onEnter={handleClickSend}
      />

      <Text type={TextEnum.H5}>{t('oversea.query3')}</Text>
      <Textinput
        onChange={(e) => {
          handleQueryChange('currentLocation', e.target.value)
        }}
        placeholder={t('oversea.query3Placeholder')}
        maxLength={300}
        withIcon={false}
        value={currentLocation}
        onEnter={handleClickSend}
      />
      <div className='mt-5 flex items-center justify-between'>
        <SearchModeSelect
          searchMode={searchMode}
          onChangeSearchMode={(mode) => {
            setSearchMode(mode)
          }}
        />
        <div className='flex items-center'>
          <Text type={TextEnum.Body_small} className={'text-secondary-black-3'}>
            {t(`homePage.tip`)}
          </Text>
          <Button className='ml-2 px-14' onClick={handleClickSend} disabled={disableSend || sended}>
            <>
              <PaperAirplaneIcon className='mr-1 h-4 w-4' />
              {t('common.send')}
            </>
          </Button>
        </div>
      </div>
      <CreditsModal
        open={openCreditsModal}
        onClose={() => {
          setOpenCreditsModal(false)
        }}
      />
    </div>
  )
}

export default OverseaRegulation
