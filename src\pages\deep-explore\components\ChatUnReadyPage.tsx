import { PDFItemType } from '@/api/pdfs/getPdf'
import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import Image from 'next/image'
import { Text, TextEnum } from '@/components/business/text'
import { Button } from '@/components/ui/button'
import { pdfRetry } from '@/api/pdfs/pdfRetry'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import { smartiesRoutes } from '@/routers'
import { useUserStore } from '@/store/userStore'
import { SubscriptionEnum } from '@/types'

export interface ChatUnReadyPageProps {
  selectedPdf?: PDFItemType
  pageStatus: 'processing' | 'retry' | 'upgrade' | 'overMaxLimits' | 'normal' | undefined
}
export const ChatUnReadyPage: React.FC<ChatUnReadyPageProps> = ({ selectedPdf, pageStatus }) => {
  const { t } = useTranslation([Namespace.DEEPEXPLORE])
  const router = useRouter()

  const user = useUserStore((state) => state.user)

  const [loading, setLoading] = useState(false)

  const handleRetry = async () => {
    if (selectedPdf?.pdfId) {
      try {
        setLoading(true)
        await pdfRetry({
          pdfId: selectedPdf.pdfId,
        })
        setLoading(false)
      } catch (error) {
        setLoading(false)
      }
    }
  }

  const handleUpgrade = () => {
    if (user?.currentPlan !== SubscriptionEnum.FREE) {
      router.push({
        pathname: smartiesRoutes.subscription,
        query: {
          tab: 'changePlan',
        },
      })
    } else {
      router.push(smartiesRoutes.pricing)
    }
  }

  useEffect(() => {
    setLoading(false)
  }, [selectedPdf?.pdfId])

  return (
    <>
      {pageStatus === 'upgrade' && (
        <div className='flex-center flex-grow flex-col'>
          <Image src='/images/process_failed.png' alt='' width={48} height={48} />
          <Text type={TextEnum.Body_medium} className='mt-1 text-muted-foreground'>
            {t('status.fileTooLarge')}
          </Text>
          <Button
            onClick={handleUpgrade}
            className='mt-5 h-9 w-[196px] rounded-sm'
            disabled={loading}>
            {t('status.upgradeBtn')}
          </Button>
        </div>
      )}
      {pageStatus === 'overMaxLimits' && (
        <div className='flex-center flex-grow flex-col'>
          <Image src='/images/process_failed.png' alt='' width={48} height={48} />
          <Text type={TextEnum.Body_medium} className='mt-1 text-muted-foreground'>
            {t('status.overMaxLimits')}
          </Text>
        </div>
      )}
      {pageStatus === 'processing' && (
        <>
          <div className='flex-center flex-grow flex-col'>
            <Image
              src='/images/downloading.png'
              alt=''
              width={48}
              height={48}
              className='animate-spin360'
            />
            <Text type={TextEnum.Body_medium} className='mt-1 text-muted-foreground'>
              {t('status.processing')}
            </Text>
          </div>
        </>
      )}
      {pageStatus === 'retry' && (
        <div className='flex-center flex-grow flex-col'>
          <Image src='/images/process_failed.png' alt='' width={48} height={48} />
          <Text type={TextEnum.Body_medium} className='mt-1 text-muted-foreground'>
            {t('status.failed')}
          </Text>
          <Button
            onClick={handleRetry}
            className='mt-5 h-9 w-[196px] rounded-sm'
            disabled={loading}>
            {t('retry')}
          </Button>
        </div>
      )}
    </>
  )
}
