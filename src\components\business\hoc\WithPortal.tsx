import type { ReactElement } from 'react'
import React from 'react'
import { createPortal } from 'react-dom'

export const isBrowser = (): boolean => typeof window !== 'undefined'

export const isServer = (): boolean => !isBrowser()

export const withPortal = function withPortal<P extends object>(
  Component: React.FC<P>,
  containerId?: string,
): React.FC<P> {
  const withPortalComponent: React.FC<P> = (props: P): ReactElement<P> => {
    if (isServer()) return <></>
    let container: null | HTMLElement = document.body

    if (containerId) {
      let target = document.getElementById(containerId)
      if (!target) {
        const newDiv = document.createElement('div')
        newDiv.id = containerId
        document.body.appendChild(newDiv)
        target = document.getElementById(containerId)
      }
      container = target
    }

    return <>{createPortal(<Component {...(props as P)} />, container as Element)}</>
  }

  return withPortalComponent
}
