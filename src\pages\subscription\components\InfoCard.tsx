import { TextEnum, Text } from '@/components/business/text'
import clsx from 'clsx'

interface InfoCardProps {
  classes: string | string[]
  header: {
    title: string
    tagText?: string
    active?: boolean
  }
  content?: {
    number: string | number
    text: string
    isText?: boolean
  }
  children?: React.ReactNode
}

const InfoCard: React.FC<InfoCardProps> = ({ classes, header, content, children }) => {
  return (
    <div className={clsx('w-[338px] border border-secondary-hover px-7 py-6', classes)}>
      {header.tagText ? (
        <div className='flex justify-between'>
          <Text type={TextEnum.H4} className={'!font-normal'}>
            {header.title}
          </Text>
          <Text
            type={TextEnum.Body_medium}
            className={clsx(
              'flex-v-center rounded px-[6px] py-1',
              header.active ? 'bg-success-10 text-success' : 'bg-dangerous-10 text-dangerous',
            )}>
            {header.tagText}
          </Text>
        </div>
      ) : (
        <Text type={TextEnum.H4} className={'!font-normal'}>
          {header.title}
        </Text>
      )}
      {content && (
        <div className='mt-[22px] flex items-end gap-1'>
          {content.isText ? (
            <Text type={TextEnum.H4}>{content.number}</Text>
          ) : (
            <Text type={TextEnum.Title_big}>{content.number}</Text>
          )}
          <Text type={TextEnum.H6} className='mb-1'>
            {content.text}
          </Text>
        </div>
      )}
      {children}
    </div>
  )
}

export default InfoCard
