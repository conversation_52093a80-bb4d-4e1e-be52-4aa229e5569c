import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import clsx from 'clsx'

export type SwitchType = 'subscription' | 'changePlan' | 'referral'

interface SwitchButtonProps {
  type: SwitchType
  onClick: (newType: SwitchType) => void
  changePlanVisible?: boolean
}
const SwitchButton: React.FC<SwitchButtonProps> = ({
  type,
  onClick,
  changePlanVisible = true,
}: SwitchButtonProps) => {
  const { t } = useTranslation(Namespace.SUBSCRIPTION)

  const handleClick = (type: SwitchType) => {
    if (onClick) onClick(type)
  }

  return (
    <div className='mt-[34px] flex h-9 w-fit cursor-pointer gap-1 rounded-sm bg-secondary p-1'>
      <div
        className={clsx(
          'flex-v-center h-[28px] rounded-sm px-4 text-[14px] leading-[16px]',
          type === 'subscription'
            ? 'ai-smarties-color-bg font-medium text-white'
            : 'font-normal text-secondary-black-3',
        )}
        onClick={() => handleClick('subscription')}>
        {t('switchBtnText.sub')}
      </div>
      {changePlanVisible ? (
        <div
          className={clsx(
            'flex-v-center h-[28px] rounded-sm px-4 text-[14px] leading-[16px]',
            type === 'changePlan'
              ? 'ai-smarties-color-bg font-medium text-white'
              : 'font-normal text-secondary-black-3',
          )}
          onClick={() => handleClick('changePlan')}>
          {t('switchBtnText.change')}
        </div>
      ) : null}
      <div
        className={clsx(
          'flex-v-center h-[28px] rounded-sm px-4 text-[14px] leading-[16px]',
          type === 'referral'
            ? 'ai-smarties-color-bg font-medium text-white'
            : 'font-normal text-secondary-black-3',
        )}
        onClick={() => handleClick('referral')}>
        {t('switchBtnText.referral')}
      </div>
    </div>
  )
}

export default SwitchButton
