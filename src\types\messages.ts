export type MarketReqMessageType = [
  {
    value: string
    key: 'description'
  },
  {
    value: string
    key: 'targetMarket'
  },
]

export type CompanyReqMessageType = [
  {
    value: string
    key: 'companyName'
  },
]

export type GeneralReqMessageType = [
  {
    value: string
    key: 'query'
  },
]

export type RegulationReqMessageType = [
  {
    value: string
    key: 'description'
  },
  {
    value: string
    key: 'targetMarket'
  },
  {
    value: string
    key: 'currentLocation'
  },
]

export type RiskReqMessageType = [
  {
    value: string
    key: 'description'
  },
]

export type SwotReqMessageType = [
  {
    value: string
    key: 'description'
  },
]

export type TopicReqMessageType = [
  {
    value: string
    key: 'topic'
  },
  {
    value: string
    key: 'details'
  },
]
export type MessageKey =
  | 'description'
  | 'targetMarket'
  | 'companyName'
  | 'query'
  | 'currentLocation'
  | 'topic'
  | 'details'

export type MessageType =
  | MarketReqMessageType
  | CompanyReqMessageType
  | GeneralReqMessageType
  | RegulationReqMessageType
  | RiskReqMessageType
  | SwotReqMessageType
  | TopicReqMessageType
