import React, { memo } from 'react'
import clsx from 'clsx'
import { ChevronDownIcon, ChevronRightIcon, TrashIcon, PlusIcon } from '@heroicons/react/24/outline'
import { Textinput } from '@/components/business/text-input'
import { OutlineItemProps } from './types'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'

/**
 * 大纲项目组件
 * 负责渲染单个大纲项目，支持编辑和展开/收起
 */
const OutlineItem: React.FC<OutlineItemProps> = memo(
  ({
    item,
    isShowDescription,
    isLast = false,
    level = 0,
    path = [],
    expandedItems,
    onToggleExpand,
    onUpdateItem,
    onDeleteChapter,
    onAddChapter,
    onAddSubChapter,
  }) => {
    const { t } = useTranslation(Namespace.DEEPRESEARCH)
    const isExpanded = expandedItems.has(item.sequence as string)
    const hasChildren = item.children && item.children.length > 0

    return (
      <div className={clsx('relative', level === 0 && !isLast && 'pb-2')}>
        {/* 时间线样式 - 仅在显示描述且为子级时显示 */}
        {isShowDescription && level > 0 && (
          <div className='absolute left-3 top-1.5 flex h-full flex-col items-center'>
            {/* 圆点 */}
            <div className='relative z-10 mt-0 h-2 w-2 rounded-full bg-primary-disabled'></div>
            {/* 连接线 - 如果不是最后一个项目则显示 */}
            {!isLast && <div className='h-full w-0.5 bg-primary-hover'></div>}
          </div>
        )}

        <div className={clsx('relative', isShowDescription && level > 0 && 'ml-8')}>
          <div
            className={clsx(
              'flex items-start leading-7',
              isShowDescription ? 'items-center justify-between' : '',
            )}>
            {/* 展开/收起按钮 - 非描述模式 */}
            {!isShowDescription &&
              hasChildren &&
              (isExpanded ? (
                <ChevronDownIcon
                  className='mr-1 size-4 translate-y-1.5 cursor-pointer'
                  onClick={() => onToggleExpand(item.sequence as string)}
                />
              ) : (
                <ChevronRightIcon
                  className='mr-1 size-4 translate-y-1.5 cursor-pointer'
                  onClick={() => onToggleExpand(item.sequence as string)}
                />
              ))}

            {/* 标题显示 */}
            {isShowDescription ? (
              <div className='flex w-full items-center justify-between'>
                <div className='text-sm text-secondary-black-3'>Chapter {item.sequence}</div>
                <div className='flex items-center gap-2'>
                  {/* Delete Chapter 按钮 */}
                  <button
                    onClick={() => onDeleteChapter?.(path)}
                    className='flex items-center gap-1 rounded border border-red-200 px-2 py-1 text-xs text-red-600 transition-colors hover:border-red-300 hover:bg-red-50'
                    title='Delete Chapter'>
                    <TrashIcon className='h-3 w-3' />
                    Delete Chapter
                  </button>
                  {/* Add 按钮 */}
                  <button
                    onClick={() => onAddChapter?.(path)}
                    className='flex items-center gap-1 rounded border border-blue-200 px-2 py-1 text-xs text-blue-600 transition-colors hover:border-blue-300 hover:bg-blue-50'
                    title='Add Chapter'>
                    <PlusIcon className='h-3 w-3' />
                    add
                  </button>
                </div>
              </div>
            ) : (
              <div>
                <span className='mr-1'>{item.sequence}</span>
                {item.title}
              </div>
            )}

            {/* 展开/收起按钮 - 描述模式 */}
            {isShowDescription && hasChildren && (
              <div className='ml-5 flex items-center text-secondary-black-3'>
                <div
                  className='flex cursor-pointer items-center'
                  onClick={() => onToggleExpand(item.sequence as string)}>
                  <ChevronRightIcon
                    className={clsx(
                      'mr-1 size-4 cursor-pointer transition-transform duration-300 ease-in-out',
                      isExpanded ? 'rotate-90' : 'rotate-0',
                    )}
                  />
                  {isExpanded ? '收起' : '展开'}
                </div>
              </div>
            )}
          </div>

          {/* 描述编辑区域 */}
          {isShowDescription && (
            <div className='mb-2 mt-1 rounded-md border border-border pb-2 text-secondary-black-2'>
              <div className='font-bold text-secondary-black-1'>
                <Textinput
                  placeholder={t('modal.titlePlaceholder') || '请输入标题'}
                  isStopEnterPrevent={false}
                  className='border-none px-2 text-[16px] leading-6 text-secondary-black-2 ring-offset-transparent focus-visible:ring-0 focus-visible:ring-offset-0'
                  value={item.title}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                    onUpdateItem(item.id!, 'title', e.target.value)
                  }
                />
              </div>
              <Textinput
                placeholder={t('modal.descriptionPlaceholder')}
                isStopEnterPrevent={false}
                className='border-none px-2 py-0 text-sm leading-6 text-secondary-black-2 ring-offset-transparent focus-visible:ring-0 focus-visible:ring-offset-0'
                value={item.description}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                  onUpdateItem(item.id!, 'description', e.target.value)
                }
              />
            </div>
          )}

          {/* 子项目 - 带展开/收起动画 */}
          {hasChildren && (
            <div
              className={clsx(
                'overflow-hidden transition-all duration-300 ease-in-out',
                isShowDescription ? 'ml-0' : 'ml-9',
                isExpanded ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0',
              )}
              style={{ perspective: '1000px' }}>
              <div
                className='transition-all duration-300 ease-in-out'
                style={{
                  transform: isExpanded ? 'rotateX(0deg) scaleY(1)' : 'rotateX(-90deg) scaleY(0.8)',
                  transformOrigin: 'center top',
                  opacity: isExpanded ? 1 : 0,
                }}>
                {item.children!.map((child, index) => (
                  <OutlineItem
                    key={child.id}
                    item={child}
                    isShowDescription={isShowDescription}
                    isLast={index === item.children!.length - 1}
                    level={level + 1}
                    path={[...path, index]}
                    expandedItems={expandedItems}
                    onToggleExpand={onToggleExpand}
                    onUpdateItem={onUpdateItem}
                    onDeleteChapter={onDeleteChapter}
                    onAddChapter={onAddChapter}
                    onAddSubChapter={onAddSubChapter}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    )
  },
)

OutlineItem.displayName = 'OutlineItem'

export default OutlineItem
