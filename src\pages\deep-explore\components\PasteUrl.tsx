import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Namespace } from '@/i18n'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { TextEnum, Text } from '@/components/business/text'

export interface PasteUrlProps {
  open: boolean
  onBack: () => void
  onConfirmPaste: (url: string) => Promise<void>
}

export const PasteUrl: React.FC<PasteUrlProps> = ({ open, onBack, onConfirmPaste }) => {
  const { t } = useTranslation([Namespace.GLOBAL, Namespace.DEEPEXPLORE])

  // paste
  const [link, setLink] = useState<string>('')
  const [disableBtn, setDisableBtn] = useState<boolean>(false)

  // reset data
  useEffect(() => {
    setLink('')
    setDisableBtn(false)
  }, [open])

  const handleConfirm = async () => {
    if (onConfirmPaste) {
      setDisableBtn(true)
      await onConfirmPaste(link)
    }
  }

  return (
    <>
      {!open ? null : (
        <>
          <Text type={TextEnum.H4}>{t('deepExplore:importFile.title')}</Text>

          <Textarea
            className='hide-scrollbar mb-5 mt-4 w-[400px] resize-none border border-dashed border-border !bg-modal-in-card'
            rows={6}
            onChange={(e) => {
              setLink(e.target.value)
            }}
          />
          <div className='text-right'>
            <Button onClick={onBack} variant='outline'>
              {t('button.return')}
            </Button>
            <Button
              onClick={handleConfirm}
              className='ml-2'
              disabled={link.trim().length === 0 || disableBtn}>
              {t('button.confirm')}
            </Button>
          </div>
        </>
      )}
    </>
  )
}
