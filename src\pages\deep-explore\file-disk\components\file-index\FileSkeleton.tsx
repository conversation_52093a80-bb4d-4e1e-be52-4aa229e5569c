import { Skeleton } from '@/components/ui/skeleton'
export const FileSkeleton = () => {
  return (
    <div className='px-[158px]'>
      <div className='mb-5 flex gap-x-2'>
        <Skeleton className='h-5 w-5' />
        <Skeleton className='h-5 w-[140px]' />
      </div>
      <div className='flex flex-wrap gap-6'>
        {Array.from({ length: 27 }).map((_, index) => (
          <Skeleton key={index} className='h-[140px] w-[140px]' />
        ))}
      </div>
    </div>
  )
}
