/* eslint-disable @typescript-eslint/no-explicit-any */
import { startQuest } from '@/api/startQuest'
import { updateSetting } from '@/api/updateSetting'
import CreditsModal from '@/components/business/credits-modal/CreditsModal'
import { SearchInput } from '@/components/business/search-input'
import { Text, TextEnum } from '@/components/business/text'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import useIsMobileScreen from '@/components/ui/useIsMobile'
import { useToast } from '@/components/ui/useToast'
import { Namespace, SupportedLangs } from '@/i18n'
import { Tracking } from '@/lib/tracking'
import { TrackingEventType } from '@/lib/tracking/types'
import { smartiesRoutes } from '@/routers'
import { User, useUserStore } from '@/store/userStore'
import { PoTypeEnum, SearchModeEnum, SubscriptionEnum } from '@/types'
import { ExclamationCircleIcon } from '@heroicons/react/24/outline'
import { useRouter } from 'next/router'
import * as React from 'react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import BasicSearchLayout from '../BasicSearchLayout'
import PageSkeleton from '../chat/PageSkeleton'
import IntroCard from './IntroCard'
import { ModalDeduct } from './modal/ModalDeduct'
import { ModalGoals } from './modal/ModalGoals'
import { ModalOutline } from './modal/ModalOutlineNew'
import { useDeepResearchStore } from '@/store/deep-research'
import { useLanguage, SUPPORTED_MAP } from '@/i18n/useLanguage'
import useWebSocketWithReconnection from '@/pages/deep-explore/socket'
// import { toast } from 'sonner'
/**
 *  需要承接websocket的信息
 */
export const checkCredits = (user?: User | null, quota = 0) => {
  if (!user) return false
  // 校验次数
  if (
    user?.currentPlan === SubscriptionEnum.FREE ||
    user?.currentPlan === SubscriptionEnum.BASIC_MONTH ||
    user?.currentPlan === SubscriptionEnum.BASIC_YEAR
  ) {
    if (user.usage.totalQuota - user.usage.totalUsage <= quota) {
      return false
    }
    return true
  } else if (
    // 不校验次数
    user?.currentPlan === SubscriptionEnum.PRO_MONTH ||
    user?.currentPlan === SubscriptionEnum.PRO_YEAR
  ) {
    return true
  } else {
    return false
  }
}

const SeacrhHomePage = () => {
  const { t } = useTranslation(Namespace.BASIC_SEARCH)
  const router = useRouter()
  const toast = useToast()
  const { query } = router
  const user = useUserStore((state) => state.user)
  const [isMobile] = useIsMobileScreen()
  const [disabled, setDisabled] = useState(false)
  const [isAutoAgent, setIsAutoAgent] = useState(user?.setting.isAutoAgent)
  const [openCreditsModal, setOpenCreditsModal] = useState(false)
  const [loading, setLoading] = useState(false)
  const [searchMode, setSearchMode] = useState<SearchModeEnum>(SearchModeEnum.AUTO)
  const [openDeductModal, setOpenDeductModal] = useState(false)
  const [openGoalsModal, setOpenGoalsModal] = useState(false)
  const [openOutlineModal, setOpenOutlineModal] = useState(false)
  // DEEPRESEARCH
  const { getRequirement, taskId, setOutline, outline } = useDeepResearchStore()
  const { language } = useLanguage({ loggedIn: true })

  // 使用 ref 来获取最新的 taskId，避免闭包陷阱
  const taskIdRef = React.useRef(taskId)
  React.useEffect(() => {
    taskIdRef.current = taskId
  }, [taskId])

  // ----------------DEEPRESEARCH  websocket ----------------
  const handleSocketMessage = (message: any) => {
    console.log('message=====>', message)
    const { type, task_id, data } = message
    console.log('taskId=====>', taskIdRef.current)
    if (task_id === taskIdRef.current) {
      if (type === 'confirm' && data.status === 'FINISH') {
        // 已经输出了大纲，把大纲存下来
        if (!data.message.合规判定) {
          return toast.error(data.message.拒绝原因)
        }
        setOutline(data.message)
      }
    }
  }
  const { startWebSocket } = useWebSocketWithReconnection({
    onMessage: handleSocketMessage,
  })
  React.useEffect(() => {
    const outlineContent = localStorage.getItem('outline')
    if (outlineContent) {
      setOutline(JSON.parse(outlineContent))
      setOpenOutlineModal(true)
    }
    if (outline.合规判定) {
      // 关闭上一个弹窗，打开 大纲弹窗
      setOpenGoalsModal(false)
      setOpenOutlineModal(true)
    }
  }, [])
  // ----------------DEEPRESEARCH  websocket ----------------
  const cardList = Object.keys(PoTypeEnum)
    .filter((item) => item !== PoTypeEnum.GENERAL)
    .map((item) => {
      return {
        title: t(`homePage.card.title_${item.toLocaleLowerCase()}`),
        content: t(`homePage.card.content_${item.toLocaleLowerCase()}`),
        searchType: item as PoTypeEnum,
      }
    })
  const initRequirement = async (question: string) => {
    setDisabled(true)
    const res = await getRequirement(
      `用户输入：${question}`,
      SUPPORTED_MAP[language as SupportedLangs],
    )
    if (!res.data?.requirement?.合规判定) {
      toast.error(res.data.requirement.拒绝原因)
      setDisabled(false)
      return
    }
    startWebSocket()
    setDisabled(false)
    setOpenGoalsModal(true)
    // 打开新的弹窗，扩写的弹窗
  }
  const handleSendQuery = async (
    queryString: string,
    mode: SearchModeEnum,
    isResearching: boolean,
  ) => {
    if (queryString.trim().length > 0) {
      setDisabled(true)
      const checkRes = checkCredits(user, isResearching ? 10 : 1)
      if (!checkRes) {
        setOpenCreditsModal(true)
        setDisabled(false)
        return false
      }
      if (isResearching) {
        const check = localStorage.getItem('ModalDeductDeepResearch')
        if (!JSON.parse(check as string)) {
          setOpenDeductModal(true)
          setDisabled(false)
          return
        }
        // 调接口来生成扩写
        await initRequirement(queryString)
        return
      }
      // start a new query
      try {
        setLoading(true)
        const resp = await startQuest({
          message: [
            {
              key: 'query',
              value: queryString,
            },
          ],
          poType: PoTypeEnum.GENERAL,
          useReasoning: mode === SearchModeEnum.REASONING ? true : undefined,
        })
        setDisabled(false)
        if (resp.sessionId && resp.processId) {
          Tracking.trackEvent('CLICK_START_CHAT_BUTTON', TrackingEventType.CLICK, {
            sessionId: resp.sessionId,
            value: queryString,
          })
          router.push({
            pathname: smartiesRoutes.basicSearch.chat(PoTypeEnum.GENERAL.toLocaleLowerCase()),
            query: {
              processId: resp.processId,
              sessionId: resp.sessionId,
            },
          })
        }
        // TODO toast 请求异常，请稍后
      } catch (error) {
        // TODO toast 请求异常，请稍后
        setDisabled(false)
        setLoading(false)
        console.error('query', error)
      }
    }
  }

  const handleClickCard = (type: PoTypeEnum) => {
    router.push(smartiesRoutes.basicSearch.query(type.toLocaleLowerCase()))
  }

  const handleUpdate = async (checked: boolean) => {
    setDisabled(true)
    setIsAutoAgent(checked)
    await updateSetting({
      isAutoAgent: checked,
    })
    setDisabled(false)
  }

  React.useEffect(() => {
    if (isMobile && query.redirectSource === 'signup') {
      toast(t('homePage.toPcNotice'), {
        duration: 3000,
        position: 'top-left',
      })
    }
  }, [isMobile, query])

  return (
    <>
      <BasicSearchLayout className='flex-center'>
        {loading ? (
          <PageSkeleton />
        ) : (
          <div
            className='mt-[160px] min-w-[860px] max-w-[1160px] flex-1 px-[130px]'
            asm-tracking='TEST_VISIT_BASIC_SEARCH_PAGE:VIEW'>
            <div className='flex w-full flex-col gap-3'>
              <SearchInput
                placeholder={t('general.placeholder')}
                maxLength={5000}
                disabled={disabled}
                onSearch={handleSendQuery}
                onEnter={handleSendQuery}
                searchMode={searchMode}
                isShowDeepSearch
                onChangeSearchMode={(mode: SearchModeEnum) => {
                  setSearchMode(mode)
                }}
              />
              <div className='flex justify-between'>
                <Text type={TextEnum.Body_small} className={'text-secondary-black-3'}>
                  {t(`homePage.tip`)}
                </Text>
                <div className='flex-center'>
                  <TooltipProvider delayDuration={0}>
                    <Tooltip>
                      <TooltipTrigger>
                        <div className='flex-center cursor-pointer'>
                          <Label htmlFor='airplane-mode' className='cursor-pointer'>
                            {t('homePage.agent')}
                          </Label>
                          <ExclamationCircleIcon className='ml-1 mr-2 h-4 w-4' />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side='bottom' align='end' className='rounded-sm bg-tooltip'>
                        <Text
                          type={TextEnum.Body_medium}
                          className='max-w-[420px] text-left text-tooltip-foreground'>
                          {t('homePage.agentDes')}
                        </Text>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <Switch
                    id='airplane-mode'
                    checked={isAutoAgent}
                    onCheckedChange={handleUpdate}
                    disabled={disabled}
                  />
                </div>
              </div>
              <div className='flex-center mt-[38px] w-full'>
                <Text type={TextEnum.H3} className='text-foreground'>
                  {t('homePage.agentTitle')}
                </Text>
              </div>
            </div>

            <div className='mt-4 grid grid-cols-3 grid-rows-2 gap-x-5 gap-y-3'>
              {cardList.map((item, index) => (
                <IntroCard
                  title={item.title}
                  content={item.content}
                  key={index}
                  type={item.searchType}
                  onClick={handleClickCard}
                />
              ))}
            </div>
          </div>
        )}
      </BasicSearchLayout>
      <CreditsModal
        open={openCreditsModal}
        onClose={() => {
          setOpenCreditsModal(false)
        }}
      />
      <ModalDeduct
        open={openDeductModal}
        handleCancel={() => {
          setDisabled(false)
          setOpenDeductModal(false)
        }}
        onConfirm={(check: boolean) => {
          if (check) {
            localStorage.setItem('ModalDeductDeepResearch', JSON.stringify(check))
          }
          setOpenDeductModal(false)
        }}></ModalDeduct>
      <ModalGoals
        open={openGoalsModal}
        handleCancel={() => {
          setOpenGoalsModal(false)
        }}></ModalGoals>
      <ModalOutline
        open={openOutlineModal}
        handleCancel={() => {
          setOpenOutlineModal(false)
        }}></ModalOutline>
    </>
  )
}

export default SeacrhHomePage
