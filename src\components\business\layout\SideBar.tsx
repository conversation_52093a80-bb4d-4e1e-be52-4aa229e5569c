import React, { useEffect, useState } from 'react'
import clsx from 'clsx'
import { useLocalStorage } from '@/lib/hooks/useLocalStorage'
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline'
import Menu, { MenuComponentHandles } from './Menu'

export const topBarHeight = 64

export interface SideBarProps {
  sidebarRef: React.RefObject<HTMLDivElement>
  children: React.ReactNode
  left?: boolean
}
const SideBar = React.forwardRef<MenuComponentHandles, SideBarProps>((props, ref) => {
  const [isCollapseLsState, setIsCollapseLsState] = useLocalStorage(
    props.left ? 'navigationLeftCollapseState' : 'navigationCollapseState',
  )
  const [isCollapse, setCollapse] = useState<boolean>(false)

  useEffect(() => {
    if (isCollapseLsState) setCollapse(isCollapseLsState === 'true')
  }, [isCollapseLsState])

  const handleCollapseClick = () => {
    const newCollapseState = !isCollapse
    setCollapse(newCollapseState)
    setIsCollapseLsState(`${newCollapseState}`)
  }

  return (
    <div
      className={clsx(
        'sticky top-0 z-50 h-screen-minus-header shrink-0 border border-r border-t-0 bg-card',
        props.left ? 'w-[372px]' : 'w-64',
        {
          '!w-0': isCollapse,
        },
      )}
      ref={props.sidebarRef}>
      {props.children ? (
        <div className='flex-1 overflow-hidden'>{props.children}</div>
      ) : (
        <div className='overflow-hidden'>
          <Menu ref={ref} />
        </div>
      )}

      <div
        className={clsx(
          'flex-center absolute top-[50%] h-10 w-4 cursor-pointer bg-primary',
          props.left ? 'rounded-l-sm' : 'rounded-r-sm',
          isCollapse
            ? props.left
              ? 'left-[-15px]'
              : 'right-[-15px]'
            : props.left
              ? 'left-[-16px]'
              : 'right-[-16px]',
        )}
        onClick={handleCollapseClick}>
        {isCollapse ? (
          props.left ? (
            <ChevronLeftIcon className='h-4 w-4 text-primary-foreground' />
          ) : (
            <ChevronRightIcon className='h-4 w-4 text-primary-foreground' />
          )
        ) : props.left ? (
          <ChevronRightIcon className='h-4 w-4 text-primary-foreground' />
        ) : (
          <ChevronLeftIcon className='h-4 w-4 text-primary-foreground' />
        )}
      </div>
    </div>
  )
})

SideBar.displayName = 'SideBar'

export default SideBar
