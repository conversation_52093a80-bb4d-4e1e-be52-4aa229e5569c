{"common": {"stop": "Stoppen", "send": "Senden"}, "homePage": {"card": {"title_market": "Marktforschung", "content_market": "KI-Ana<PERSON>sten bereit", "title_topic": "Themenrecherche", "content_topic": "Auto-<PERSON><PERSON> mehr<PERSON>", "title_swot": "SWOT-Analyse", "content_swot": "Stärken und Schwächen erkennen", "title_company": "Firmenforschung", "content_company": "Business Canvas in Minuten", "title_regulation": "Exportkonformität", "content_regulation": "Exportwissen auf einen Blick", "title_risk": "Risiken", "content_risk": "Risiken identifizieren und sichern"}, "auto": "Auto", "reasoning": "Begründung", "sendNotification": "Benachrichtigen Sie mich", "deepThinkFinish": "Reflektionen abgeschlossen", "deepSearchCompleted": "Der Bericht wird erstellt.", "checkDeepSearchResult": "Zur Ansicht klicken", "deepSearchProgressDesc": "<PERSON><PERSON>, Analyse und Zusammenfassung der Frage Fortschritt ist: ", "deepSearch": "<PERSON><PERSON><PERSON>", "deepSearchAgent": "Assistent für die Tiefensuche", "deepSearchQuestionDescription": "Auf der Grundlage Ihrer Angaben werden wir die Fragen erweitern, damit Sie die Informationen besser abrufen und zusammenfassen können", "tip": "KI kann irren. Wichtiges prüfen.", "agent": "Smart-Suche", "agentDes": "Smart-Suche findet das beste Tool. Deaktivieren stoppt Toolwechsel.", "agentTitle": "Tools für bessere Ergebnisse wählen", "toPcNotice": "Vielen Dank für die Registrierung, bitte erhalten Sie eine bessere Menge Erfahrung auf Ihrem Computer."}, "general": {"query": "Frage", "placeholder": "Fragen oder URL einfügen"}, "market": {"query1": "Produkt/Geschäft", "query1Placeholder": "z.B. 'GPS-Tracker für Haustiere'", "tips1": "<PERSON><PERSON><PERSON>, gezielte Fragen stellen", "query2": "Zielmarkt", "query2Placeholder": "z.B. 'Global' oder 'Europa'", "tips2": "Region oder global wählen"}, "topic": {"query1": "<PERSON>a", "query1Placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON>", "tips1": "<PERSON><PERSON><PERSON>, gezielte Fragen stellen", "query2": "Details", "query2Placeholder": "Schlüsselpunkte angeben", "tips2": "Relevante Details hinzufügen"}, "swot": {"query1": "Produkt/Geschäft", "query1Placeholder": "z.B. GPS-Tracker: <PERSON><PERSON><PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON>, sicher", "tips1": "<PERSON><PERSON> be<PERSON>, wir leiten Si<PERSON>"}, "company": {"query1": "Firmenname", "query1Placeholder": "z.B. 'OpenAI'", "tips1": "Bei Bedarf Details hinzufügen"}, "oversea": {"query1": "Produkt/Dienstleistung", "query1Placeholder": "z.B. 'Wäschedesinfektionsmittel'", "tips1": "Exportanforderungen überblicken", "query2": "Zielmarkt", "query2Placeholder": "z.B. 'USA'", "query3": "Aktueller Standort", "query3Placeholder": "z.B. 'Spanien'"}, "risk": {"query1": "Produkt/Geschäft", "query1Placeholder": "z.B. 'GPS-Tracker-Export Singapur-USA'", "tips1": "R<PERSON><PERSON> er<PERSON>, Gegenmaßnahmen planen"}, "chat": {"statustext": {"search": "<PERSON><PERSON>", "extract": "Extrahieren", "summarize": "Zusammenfassen"}, "input": {"placehoder": "Nachfragen"}, "rightSide": {"recommend": "Vertiefen", "recommendTip": "Klicken Sie für die Details", "noData": "<PERSON><PERSON>", "PDF": "PDF", "save": "Speichern", "viewMore": "<PERSON><PERSON>", "open": "<PERSON><PERSON><PERSON>", "saveToDoc": "Speichern"}, "refer": "<PERSON><PERSON><PERSON><PERSON>"}, "stopModal": {"content": "Generierung stoppen?"}, "imgChat": {"describeImg": "Bild be<PERSON>"}}