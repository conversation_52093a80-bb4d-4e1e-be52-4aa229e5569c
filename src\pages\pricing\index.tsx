import { getUser } from '@/api/getUser'
import { refreshPlan } from '@/api/subscription/refreshPlan'
import { PageHeader } from '@/components/business/page-header'
import { Text, TextEnum } from '@/components/business/text'
import { useTheme } from '@/context/ThemeContext'
import { Namespace } from '@/i18n'
import { smartiesRoutes } from '@/routers'
import { useUserStore } from '@/store/userStore'
import { SubscriptionEnum } from '@/types'
import { Environments, initializePaddle, Paddle, PaddleEventData, Theme } from '@paddle/paddle-js'
import { useRouter } from 'next/router'
import { useEffect, useRef, useState } from 'react'
import { Trans, useTranslation } from 'react-i18next'
import { toast } from 'sonner'
import { subscriptionPriceId } from '../subscription/utils/getPriceId'
import PricingCard from './PricingCard'
import SwitchButton from './SwitchButton'
const paddleEnv = process.env.NEXT_PUBLIC_PADDLE_ENV! as Environments
const paddleToken = process.env.NEXT_PUBLIC_PADDLE_CLIENT_TOKEN!

export const prices = {
  basic: {
    month: '$18',
    year: '$180',
  },
  pro: {
    month: '$35',
    year: '$350',
  },
}

const Pricing = () => {
  const { theme } = useTheme()
  const router = useRouter()
  const user = useUserStore((state) => state.user)
  const updateUser = useUserStore((state) => state.updateUser)

  const { t } = useTranslation(Namespace.PRICING)
  const [sending, setSending] = useState<SubscriptionEnum | undefined>(undefined)

  const [selectedType, setSelectedType] = useState<'month' | 'year'>('month')

  const [paddle, setPaddle] = useState<Paddle>()

  const lastPaddleEvent = useRef<undefined | string>(undefined)

  const handleUpgrade = async ({ type }: { type: SubscriptionEnum }) => {
    // 拦截
    setSending(type)
    const res = await refreshPlan()
    if (!res && type) {
      openCheckout(type)
    } else {
      setSending(undefined)
      toast(<div className='text-dangerous'>System Error.</div>, {
        position: 'top-center',
        duration: 5000,
      })
    }
  }

  const openCheckout = (type: SubscriptionEnum) => {
    if (type === SubscriptionEnum.FREE) {
      return
    }
    if (user?.email && user.userId)
      paddle?.Checkout.open({
        settings: {
          theme: theme as Theme,
          locale: user?.setting.language === 'zh' ? 'zh-Hans' : 'en',
        },
        items: [{ priceId: subscriptionPriceId[type], quantity: 1 }],
        customData: {
          userId: user?.userId,
          userEmail: user?.email,
        },
        customer: {
          email: user?.email,
        },
      })
  }

  useEffect(() => {
    if (user && user?.currentPlan !== SubscriptionEnum.FREE) {
      router.push(smartiesRoutes.subscription)
    }
  }, [user])

  useEffect(() => {
    if (!paddleEnv || !paddleToken || !user?.email) {
      return
    }

    initializePaddle({
      environment: paddleEnv,
      token: paddleToken,
      pwCustomer: {
        email: user?.email,
      }, // support retain
      eventCallback: (event: PaddleEventData) => {
        if (event.name === 'checkout.closed' && lastPaddleEvent.current === 'checkout.completed') {
          getUser().then((data) => {
            if (data.userId) {
              updateUser(data)
            }
            router.push(smartiesRoutes.subscription)
          })
        } else {
          // console.log(event.name, event?.data?.transaction_id, event?.data?.status)
          lastPaddleEvent.current = event.name
          if (event.name == 'checkout.loaded') {
            setSending(undefined)
          }
        }
      },
      checkout: {
        settings: {
          allowLogout: false,
          displayMode: 'overlay',
          allowedPaymentMethods: [
            'alipay',
            'apple_pay',
            'bancontact',
            'card',
            'google_pay',
            'ideal',
            'paypal',
          ],
        },
      },
    }).then((paddleInstance: Paddle | undefined) => {
      if (paddleInstance) {
        setPaddle(paddleInstance)
      }
      // paddleInstance
      //   ?.PricePreview({
      //     items: [
      //       {
      //         priceId: PriceItems.month,
      //         quantity: 1,
      //       },
      //       {
      //         priceId: PriceItems.year,
      //         quantity: 1,
      //       },
      //     ],
      //   })
      //   .then((result) => {
      //     console.log(result)
      //     setMonthlyPrice(
      //       result.data.details.lineItems[0].formattedTotals.subtotal as unknown as number,
      //     )
      //     setYearPrice(
      //       result.data.details.lineItems[1].formattedTotals.subtotal as unknown as number,
      //     )
      //   })
    })
  }, [user])

  return (
    <>
      <div
        className='min-h-screen min-w-fit bg-background pb-16'
        asm-tracking='TEST_VISIT_PRICING_PAGE:VIEW'>
        <PageHeader classes='fixed top-0 z-10' />

        <div className='flex-v-center mt-36 flex flex-col'>
          <Text type={TextEnum.H1}>{t('title')}</Text>
          <SwitchButton type={selectedType} onClick={setSelectedType} />
          <div className='flex gap-6'>
            <PricingCard
              title={t('card.type.free')}
              pricingNode={
                <Text type={TextEnum.Title_big} className='text-primary'>
                  {t('card.type.free')}
                </Text>
              }
              contents={{
                credits: [
                  {
                    value: t('card.content.credits.free'),
                    support: true,
                  },
                ],
                exploration: [
                  {
                    value: t('card.content.exploration.item1'),
                    support: true,
                  },
                  {
                    value: t('card.content.exploration.item2'),
                    support: true,
                  },
                  {
                    value: t('card.content.exploration.item3'),
                    support: true,
                  },
                  {
                    value: t('card.content.exploration.item4'),
                    support: true,
                  },
                ],
                file: [
                  {
                    value: t('card.content.fileStorage.free.item1'),
                    support: true,
                  },
                  {
                    value: t('card.content.fileStorage.basic.item2'),
                    support: true,
                  },
                ],
              }}
              buttonType='secondary'
              buttonText={t('card.button.currentPlan')}
              disabled={false}
              loading={false}
            />
            <PricingCard
              title={t('card.type.basic')}
              subTitle={selectedType === 'year' ? t('card.pricing.discount') : ''}
              pricingNode={
                <div className='flex flex-row items-end'>
                  <Text type={TextEnum.Title_big} className='text-primary'>
                    {prices.basic[selectedType]}
                  </Text>
                  <Text type={TextEnum.Body_medium} className='mb-2 text-secondary-black-2'>
                    /{t(`card.pricing.${selectedType}`)}
                  </Text>
                </div>
              }
              contents={{
                credits: [
                  {
                    value:
                      selectedType === 'month'
                        ? t('card.content.credits.basic_mon')
                        : t('card.content.credits.basic_year'),
                    support: true,
                  },
                ],
                exploration: [
                  {
                    value: t('card.content.exploration.item1'),
                    support: true,
                  },
                  {
                    value: t('card.content.exploration.item2'),
                    support: true,
                  },
                  {
                    value: t('card.content.exploration.item3'),
                    support: true,
                  },
                  {
                    value: t('card.content.exploration.item4'),
                    support: true,
                  },
                ],
                file: [
                  {
                    value: t('card.content.fileStorage.basic.item1'),
                    support: true,
                  },
                  {
                    value: t('card.content.fileStorage.basic.item2'),
                    support: true,
                  },
                ],
              }}
              buttonText={t('card.button.upgrade')}
              onClick={() => {
                handleUpgrade({
                  type:
                    selectedType === 'month'
                      ? SubscriptionEnum.BASIC_MONTH
                      : SubscriptionEnum.BASIC_YEAR,
                })
              }}
              disabled={
                sending !== undefined &&
                sending !== SubscriptionEnum.BASIC_MONTH &&
                sending !== SubscriptionEnum.BASIC_YEAR
              }
              loading={
                sending === SubscriptionEnum.BASIC_MONTH || sending === SubscriptionEnum.BASIC_YEAR
              }
            />
            <PricingCard
              title={t('card.type.professional')}
              subTitle={selectedType === 'year' ? t('card.pricing.discount') : ''}
              pricingNode={
                <div className='flex flex-row items-end'>
                  <Text type={TextEnum.Title_big} className='text-primary'>
                    {prices.pro[selectedType]}
                  </Text>
                  <Text type={TextEnum.Body_medium} className='mb-2 text-secondary-black-2'>
                    /{t(`card.pricing.${selectedType}`)}
                  </Text>
                </div>
              }
              contents={{
                credits: [
                  {
                    value: t('card.content.credits.pro'),
                    support: true,
                  },
                ],
                exploration: [
                  {
                    value: t('card.content.exploration.item1'),
                    support: true,
                  },
                  {
                    value: t('card.content.exploration.item2'),
                    support: true,
                  },
                  {
                    value: t('card.content.exploration.item3'),
                    support: true,
                  },
                  {
                    value: t('card.content.exploration.item4'),
                    support: true,
                  },
                ],
                file: [
                  {
                    value: t('card.content.fileStorage.pro.item1'),
                    support: true,
                  },
                  {
                    value: t('card.content.fileStorage.pro.item2'),
                    support: true,
                  },
                  {
                    value: t('card.content.fileStorage.pro.item3'),
                    support: false,
                  },
                ],
                exclusiveFeat: [
                  {
                    value: t('card.content.exclusiveFeature.item1'),
                    support: true,
                  },
                ],
              }}
              buttonText={t('card.button.upgrade')}
              onClick={() => {
                handleUpgrade({
                  type:
                    selectedType === 'month'
                      ? SubscriptionEnum.PRO_MONTH
                      : SubscriptionEnum.PRO_YEAR,
                })
              }}
              disabled={
                sending !== undefined &&
                sending !== SubscriptionEnum.PRO_MONTH &&
                sending !== SubscriptionEnum.PRO_YEAR
              }
              loading={
                sending === SubscriptionEnum.PRO_MONTH || sending === SubscriptionEnum.PRO_YEAR
              }
            />
            <PricingCard
              title={t('card.type.bespoke')}
              pricingNode={
                <>
                  <div className='flex items-end'>
                    <Text type={TextEnum.Body_medium} className='text-secondary-black-2'>
                      <Trans
                        i18nKey='card.pricing.custom'
                        t={t}
                        components={[
                          <Text
                            type={TextEnum.Title_big}
                            className='inline-block text-primary'
                            key={0}>
                            {prices.pro[selectedType]}
                          </Text>,
                        ]}
                        values={{ price: '$75' }}
                      />
                    </Text>
                  </div>
                </>
              }
              contents={{
                bespoke: [
                  {
                    value: t('card.content.bespoke.item1'),
                    support: true,
                  },
                  {
                    value: t('card.content.bespoke.item2'),
                    support: true,
                  },
                  {
                    value: t('card.content.bespoke.item3'),
                    support: true,
                  },
                  {
                    value: t('card.content.bespoke.item4'),
                    support: true,
                  },
                  {
                    value: t('card.content.bespoke.item5'),
                    support: true,
                  },
                ],
              }}
              buttonText={t('card.button.contact')}
              onClick={() => {
                window.open(
                  'https://docs.google.com/forms/d/e/1FAIpQLSdDSFk9un3IlTznm2U98eUakf9BX7k4J49O9f2pfdwiMgvtpw/viewform',
                  '_blank',
                )
              }}
              disabled={false}
              loading={false}
            />
          </div>
        </div>
      </div>
    </>
  )
}

export default Pricing
