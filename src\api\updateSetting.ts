import { SupportedLangs } from '@/i18n'
import { fetchWithAuth } from '@/lib/fetch'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface UpdateSettingRequest {
  language?: SupportedLangs
  isAutoAgent?: boolean
  disableToolbar?: boolean
}

export interface UpdateSettingResponse {
  language: SupportedLangs
  isAutoAgent: boolean
  disableToolbar: boolean
}

export const updateSetting = async (data: UpdateSettingRequest): Promise<UpdateSettingResponse> => {
  const response = await fetchWithAuth<UpdateSettingResponse>(`${apiUrl}/user/updateSetting`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
