import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import PricingCard from './components/PricingCard'
import { TextEnum, Text } from '@/components/business/text'
import { SubscriptionEnum } from '@/types'
import { useUserStore } from '@/store/userStore'
import CancelPlanModal from './components/CancelPlanModal'
import { useState } from 'react'
import UnableChangeModal from './components/UnableChangeModal'
import ChangeModal from './components/ChangeModal'
import { cancelSubscription } from '@/api/subscription/cancelSubscription'
import { GetSubscriptionDetailResponse } from '@/api/subscription/getSubscriptionDetail'
import { useRouter } from 'next/router'
import { getPrice } from './utils/getPrice'
import { changeSubscription } from '@/api/subscription/changeSubscription'
import { getPriceId } from './utils/getPriceId'
import { getUser } from '@/api/getUser'
import { isMonthPlan } from './utils/plan'

const ChangePlanPage = ({
  subscriptionDetail,
  onChangePage,
}: {
  onChangePage: () => void
  subscriptionDetail?: GetSubscriptionDetailResponse
}) => {
  const { t } = useTranslation(Namespace.SUBSCRIPTION)
  const router = useRouter()

  const user = useUserStore((state) => state.user)
  const updateUser = useUserStore((state) => state.updateUser)

  const [openCancelModal, setOpenCancelModal] = useState<boolean>(false)
  const [openUnableChangeModal, setUnableChangeModal] = useState<boolean>(false)
  const [openChangeModal, setChangeModal] = useState<boolean>(false)

  const [requestingChange, setRequestingChange] = useState<boolean>(false)

  const [newPlan, setNewPlan] = useState<SubscriptionEnum | undefined>(undefined)

  const handlCancelPlan = async () => {
    if (subscriptionDetail?.subscriptionId) {
      const res = await cancelSubscription({ subscriptionId: subscriptionDetail?.subscriptionId })
      if (res.result === 'ok') {
        setOpenCancelModal(false)
        router.reload()
      }
    }
  }

  const handlChangePlan = async () => {
    if (subscriptionDetail?.subscriptionId) {
      try {
        setRequestingChange(true)
        const res = await changeSubscription({
          subscriptionId: subscriptionDetail?.subscriptionId,
          newPriceId: getPriceId(newPlan)!,
          ifNow: true,
        })
        if (res.result === 'ok') {
          // 更新成功
          getUser().then((data) => {
            setRequestingChange(false)
            setChangeModal(false)
            if (data.userId) {
              updateUser(data)
              onChangePage()
            }
          })
        } else {
          setRequestingChange(false)
          setChangeModal(false)
          setUnableChangeModal(true)
        }
      } catch (error) {
        // 更新异常
        setRequestingChange(false)
      }
    }
  }

  const handleClickSelectPlan = ({ newPlan }: { newPlan: SubscriptionEnum }) => {
    setChangeModal(true)
    setNewPlan(newPlan)
  }

  return (
    <>
      {user?.currentPlan && isMonthPlan(user?.currentPlan) && (
        <>
          <Text type={TextEnum.H4} className='mb-4 mt-7'>
            {t('common.monthly')}
          </Text>

          <div className='flex gap-5'>
            <PricingCard
              title={t('cardInfo.basic')}
              price={getPrice(SubscriptionEnum.BASIC_MONTH)}
              billingCycle='month'
              messages={t('cardInfo.messages.basic_mon')}
              nextBilledAt={subscriptionDetail?.nextBilledAt}
              curPeriodEndsAt={subscriptionDetail?.curPeriodEndsAt}
              isCurrentPlan={user?.currentPlan === SubscriptionEnum.BASIC_MONTH}
              subScription={SubscriptionEnum.BASIC_MONTH}
              onSelect={handleClickSelectPlan}
              onCancel={() => {
                setOpenCancelModal(true)
              }}
              space={Boolean(user?.currentPlan === SubscriptionEnum.PRO_MONTH)}
            />

            <PricingCard
              title={t('cardInfo.pro')}
              price={getPrice(SubscriptionEnum.PRO_MONTH)}
              billingCycle='month'
              messages={t('cardInfo.messages.pro')}
              nextBilledAt={subscriptionDetail?.nextBilledAt}
              curPeriodEndsAt={subscriptionDetail?.curPeriodEndsAt}
              isCurrentPlan={user?.currentPlan === SubscriptionEnum.PRO_MONTH}
              subScription={SubscriptionEnum.PRO_MONTH}
              onSelect={handleClickSelectPlan}
              onCancel={() => {
                setOpenCancelModal(true)
              }}
              space={Boolean(user?.currentPlan === SubscriptionEnum.BASIC_MONTH)}
            />
          </div>
        </>
      )}

      <Text type={TextEnum.H4} className='mb-1 mt-7'>
        {t('common.yearly')}
      </Text>
      <div className='mb-4 font-[13px] leading-5 text-secondary-black-3'>{t('discount')}</div>

      <div className='flex gap-5'>
        <PricingCard
          title={t('cardInfo.basic')}
          price={getPrice(SubscriptionEnum.BASIC_YEAR)}
          billingCycle='year'
          messages={t('cardInfo.messages.basic_pro')}
          nextBilledAt={subscriptionDetail?.nextBilledAt}
          curPeriodEndsAt={subscriptionDetail?.curPeriodEndsAt}
          isCurrentPlan={user?.currentPlan === SubscriptionEnum.BASIC_YEAR}
          subScription={SubscriptionEnum.BASIC_YEAR}
          onSelect={handleClickSelectPlan}
          onCancel={() => {
            setOpenCancelModal(true)
          }}
          space={Boolean(user?.currentPlan === SubscriptionEnum.PRO_YEAR)}
        />

        <PricingCard
          title={t('cardInfo.pro')}
          price={getPrice(SubscriptionEnum.PRO_YEAR)}
          billingCycle='year'
          messages={t('cardInfo.messages.pro')}
          nextBilledAt={subscriptionDetail?.nextBilledAt}
          curPeriodEndsAt={subscriptionDetail?.curPeriodEndsAt}
          isCurrentPlan={user?.currentPlan === SubscriptionEnum.PRO_YEAR}
          subScription={SubscriptionEnum.PRO_YEAR}
          onSelect={handleClickSelectPlan}
          onCancel={() => {
            setOpenCancelModal(true)
          }}
          space={Boolean(user?.currentPlan === SubscriptionEnum.BASIC_YEAR)}
        />
      </div>

      <CancelPlanModal
        open={openCancelModal}
        onClose={() => {
          setOpenCancelModal(false)
        }}
        onConfirm={handlCancelPlan}
        curPeriodEndsAt={subscriptionDetail?.curPeriodEndsAt}
      />
      <UnableChangeModal
        open={openUnableChangeModal}
        onClose={() => {
          setUnableChangeModal(false)
        }}
        onConfirm={() => {
          setUnableChangeModal(false)
        }}
      />

      <ChangeModal
        open={openChangeModal}
        onClose={() => {
          setChangeModal(false)
        }}
        disabled={requestingChange}
        onConfirm={handlChangePlan}
      />
    </>
  )
}

export default ChangePlanPage
