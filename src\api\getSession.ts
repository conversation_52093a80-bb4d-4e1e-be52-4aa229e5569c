import { fetchWithAuth } from '@/lib/fetch'
import { MessageType, PoTypeEnum } from '@/types/index'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export enum MessageRoleEnum {
  USER = 'user',
  ASSISTANT = 'assistant',
}

// 定义具体接口
export interface UserMessage {
  role: MessageRoleEnum.USER
  content: MessageType
  timestamp?: number
  processId?: string
}

export interface ReferenceItem {
  url: string
  webcrawlerId: string
  title?: string
  favicon?: string
}

export interface AssistantMessage {
  role: MessageRoleEnum.ASSISTANT
  content: string
  reasoning?: string
  timestamp?: number
  processId: string
  reportSourceId?: string
  referenceList?: ReferenceItem[]
}

export type SingleMessageType = UserMessage | AssistantMessage

export interface GetSessionRequest {
  sessionId: string
}
export interface GetSessionResponse {
  sessionId: string
  userId: string
  poType: PoTypeEnum
  createdAt: number
  messages: SingleMessageType[]
}

export const getSession = async (data: GetSessionRequest): Promise<GetSessionResponse> => {
  const response = await fetchWithAuth<GetSessionResponse>(`${apiUrl}/session/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
