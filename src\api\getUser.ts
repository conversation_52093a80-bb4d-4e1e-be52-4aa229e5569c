import { fetchWithAuth } from '@/lib/fetch'
import { SupportedLangs } from '@/i18n'
import { isServer } from '@/lib/environments'
import { SubscriptionEnum } from '@/types'

const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface getUserResponse {
  userId: string
  name: string
  email: string
  picture: string
  setting: {
    language: SupportedLangs
    isAutoAgent: boolean
    disableToolbar: boolean
  }
  usage: {
    freeQuota: number
    freeUsage: number
    paidQuota: number
    paidUsage: number
    totalQuota: number
    totalUsage: number
  }
  currentPlan: SubscriptionEnum
}

export interface getUserRequest {
  token?: string
}

export const getUser = async (data?: getUserRequest): Promise<getUserResponse> => {
  if (data && data.token) {
    const response = await fetch(`${apiUrl}/user/me`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        authorization: `Bearer ${data.token}`,
      },
    })
    if (!response.ok) {
      if (response.status === 403 && isServer()) {
        return Promise.reject({
          status: 403,
          message: 'From Server Forbidden: Redirecting to login...',
          redirectUrl: '/logout',
        })
      }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const error = new Error(`HTTP error! Status: ${response.status}`) as any
      error.status = response.status
      throw error
    }
    return (await response.json()) as getUserResponse
  } else {
    const response = await fetchWithAuth<getUserResponse>(`${apiUrl}/user/me`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    return response
  }
}
