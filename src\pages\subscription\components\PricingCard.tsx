import { TextEnum, Text } from '@/components/business/text'
import { Button } from '@/components/ui/button'
import { Namespace, SupportedLangs } from '@/i18n'
import { CheckBadgeIcon, EllipsisHorizontalIcon } from '@heroicons/react/24/outline'
import clsx from 'clsx'
import { TFunction } from 'i18next'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import PlanDetailsModal, { ContentType } from './PlanDetailsModal'
import { SubscriptionEnum } from '@/types'
import { useUserStore } from '@/store/userStore'
import { formatTime } from '../utils/formatTime'

interface PricingCardPriceProps {
  price: string
  billingText: string
}

const PricingCardPrice: React.FC<PricingCardPriceProps> = ({ price, billingText }) => (
  <div className='flex items-end'>
    <Text type={TextEnum.Title_big} className='text-primary'>
      {price}
    </Text>
    <Text type={TextEnum.H6} className='mb-2 text-secondary-black-2'>
      {billingText}
    </Text>
  </div>
)

interface PricingCardButtonProps {
  isCurrentPlan: boolean
  btnText: string
  onSelect: () => void
}

const PricingCardButton: React.FC<PricingCardButtonProps> = ({
  isCurrentPlan,
  onSelect,
  btnText,
}) => (
  <Button
    onClick={isCurrentPlan ? undefined : onSelect}
    className={clsx('h-10 w-[114px]', isCurrentPlan ? 'cursor-default' : 'cursor-pointer')}
    variant={isCurrentPlan ? 'secondary' : 'default'}>
    {btnText}
  </Button>
)

interface PricingCardDetailsProps {
  title: string
  messages: string
  t: TFunction<Namespace.SUBSCRIPTION, undefined>
  onClick: () => void
}

const PricingCardDetails: React.FC<PricingCardDetailsProps> = ({ title, messages, t, onClick }) => (
  <div className='border-t pt-4'>
    <Text type={TextEnum.H6}>{title}</Text>

    <div className='flex items-center gap-1 py-4'>
      <CheckBadgeIcon className='h-4 w-4 shrink-0 text-primary' />
      <Text type={TextEnum.Body_big} className={'text-secondary-black-2'}>
        {messages}
      </Text>
    </div>

    <div className='ml-2 flex cursor-pointer gap-1'>
      <EllipsisHorizontalIcon className='h-6 w-6 text-secondary-black-3' />
      <Text type={TextEnum.Body_big} className='text-secondary-black-3' onClick={onClick}>
        {t('cardInfo.more')}
      </Text>
    </div>
  </div>
)

interface PricingCardProps {
  title: string
  price: string
  billingCycle: 'month' | 'year'
  messages: string
  isCurrentPlan: boolean
  nextBilledAt?: number | null
  curPeriodEndsAt?: number | null
  onSelect: ({ newPlan }: { newPlan: SubscriptionEnum }) => void
  onCancel: () => void
  subScription: SubscriptionEnum
  space: boolean
}

// PricingCard 组件
const PricingCard: React.FC<PricingCardProps> = ({
  title,
  price,
  billingCycle,
  messages,
  isCurrentPlan,
  nextBilledAt,
  curPeriodEndsAt,
  onSelect,
  onCancel,
  subScription,
  space,
}) => {
  const { t } = useTranslation([Namespace.SUBSCRIPTION, Namespace.PRICING])
  const [openDetailModal, setOpenDetailModal] = useState<boolean>(false)
  const user = useUserStore((state) => state.user)

  const [content, setContent] = useState<ContentType>()

  useEffect(() => {
    switch (subScription) {
      case SubscriptionEnum.BASIC_MONTH:
        setContent({
          plan: SubscriptionEnum.BASIC_MONTH,
          title: t('cardInfo.basic'),
          price: '$18',
          priceText: t('common.monthText'),
          contents: {
            credits: [
              {
                value: t('pricing:card.content.credits.basic_mon'),
                support: true,
              },
            ],
            exploration: [
              {
                value: t('pricing:card.content.exploration.item1'),
                support: true,
              },
              {
                value: t('pricing:card.content.exploration.item2'),
                support: true,
              },
              {
                value: t('pricing:card.content.exploration.item3'),
                support: true,
              },
              {
                value: t('pricing:card.content.exploration.item4'),
                support: true,
              },
            ],
            file: [
              {
                value: t('pricing:card.content.fileStorage.basic.item1'),
                support: true,
              },
              {
                value: t('pricing:card.content.fileStorage.basic.item2'),
                support: true,
              },
            ],
          },
        })
        break
      case SubscriptionEnum.BASIC_YEAR:
        setContent({
          plan: SubscriptionEnum.BASIC_YEAR,
          title: t('cardInfo.basic'),
          price: '$180',
          priceText: t('common.yearText'),
          contents: {
            credits: [
              {
                value: t('pricing:card.content.credits.basic_year'),
                support: true,
              },
            ],
            exploration: [
              {
                value: t('pricing:card.content.exploration.item1'),
                support: true,
              },
              {
                value: t('pricing:card.content.exploration.item2'),
                support: true,
              },
              {
                value: t('pricing:card.content.exploration.item3'),
                support: true,
              },
              {
                value: t('pricing:card.content.exploration.item4'),
                support: true,
              },
            ],
            file: [
              {
                value: t('pricing:card.content.fileStorage.basic.item1'),
                support: true,
              },
              {
                value: t('pricing:card.content.fileStorage.basic.item2'),
                support: true,
              },
            ],
          },
        })
        break
      case SubscriptionEnum.PRO_MONTH:
        setContent({
          plan: SubscriptionEnum.PRO_MONTH,
          title: t('cardInfo.pro'),
          price: '$35',
          priceText: t('common.monthText'),
          contents: {
            credits: [
              {
                value: t('pricing:card.content.credits.pro'),
                support: true,
              },
            ],
            exploration: [
              {
                value: t('pricing:card.content.exploration.item1'),
                support: true,
              },
              {
                value: t('pricing:card.content.exploration.item2'),
                support: true,
              },
              {
                value: t('pricing:card.content.exploration.item3'),
                support: true,
              },
              {
                value: t('pricing:card.content.exploration.item4'),
                support: true,
              },
            ],
            file: [
              {
                value: t('pricing:card.content.fileStorage.pro.item1'),
                support: true,
              },
              {
                value: t('pricing:card.content.fileStorage.pro.item2'),
                support: true,
              },
              {
                value: t('pricing:card.content.fileStorage.pro.item3'),
                support: false,
              },
            ],
            exclusiveFeat: [
              {
                value: t('pricing:card.content.exclusiveFeature.item1'),
                support: true,
              },
            ],
          },
        })
        break
      case SubscriptionEnum.PRO_YEAR:
        setContent({
          plan: SubscriptionEnum.PRO_YEAR,
          title: t('cardInfo.pro'),
          price: '$350',
          priceText: t('common.yearText'),
          contents: {
            credits: [
              {
                value: t('pricing:card.content.credits.pro'),
                support: true,
              },
            ],
            exploration: [
              {
                value: t('pricing:card.content.exploration.item1'),
                support: true,
              },
              {
                value: t('pricing:card.content.exploration.item2'),
                support: true,
              },
              {
                value: t('pricing:card.content.exploration.item3'),
                support: true,
              },
              {
                value: t('pricing:card.content.exploration.item4'),
                support: true,
              },
            ],
            file: [
              {
                value: t('pricing:card.content.fileStorage.pro.item1'),
                support: true,
              },
              {
                value: t('pricing:card.content.fileStorage.pro.item2'),
                support: true,
              },
              {
                value: t('pricing:card.content.fileStorage.pro.item3'),
                support: false,
              },
            ],
            exclusiveFeat: [
              {
                value: t('pricing:card.content.exclusiveFeature.item1'),
                support: true,
              },
            ],
          },
        })
        break
      default:
        break
    }
  }, [subScription])

  return (
    <>
      <div
        className={clsx(
          'w-[498px] min-w-[498px] rounded-lg border p-6 pt-5',
          isCurrentPlan ? 'border-primary-border bg-primary-bg' : 'border-border bg-card',
        )}>
        {/* title */}
        <Text type={TextEnum.H4}>{title}</Text>

        {/* price section */}
        <div
          className={clsx(
            'mt-4 flex justify-between',
            space ? 'mb-12' : 'mb-4',
            isCurrentPlan ? '!mb-0' : '',
          )}>
          <PricingCardPrice
            price={price}
            billingText={billingCycle === 'month' ? t('common.monthText') : t('common.yearText')}
          />
          <PricingCardButton
            isCurrentPlan={isCurrentPlan}
            onSelect={() => {
              onSelect({ newPlan: subScription })
            }}
            btnText={isCurrentPlan ? t('cardInfo.btn.current') : t('cardInfo.btn.select')}
          />
        </div>

        {isCurrentPlan && nextBilledAt && (
          <p className='mb-4 mt-3 flex gap-2 text-sm font-medium text-secondary-black-3'>
            {t('cardInfo.renew')}&nbsp;&nbsp;
            {formatTime(nextBilledAt as number, user?.setting.language as SupportedLangs)}
            <span className='cursor-pointer text-primary' onClick={onCancel}>
              {t('cardInfo.btn.cancle')}
            </span>
          </p>
        )}

        {isCurrentPlan && nextBilledAt === null && curPeriodEndsAt && (
          <p className='mb-4 mt-3 flex gap-2 text-sm font-medium text-secondary-black-3'>
            {t('cardInfo.expires')}:&nbsp;&nbsp;
            {formatTime(curPeriodEndsAt as number, user?.setting.language as SupportedLangs)}
          </p>
        )}

        <PricingCardDetails
          title={t('cardInfo.message')}
          messages={messages}
          t={t}
          onClick={() => {
            setOpenDetailModal(true)
          }}
        />
      </div>

      <PlanDetailsModal
        open={openDetailModal}
        onClose={() => {
          setOpenDetailModal(false)
        }}
        onSelect={onSelect}
        content={content}
      />
    </>
  )
}

export default PricingCard
